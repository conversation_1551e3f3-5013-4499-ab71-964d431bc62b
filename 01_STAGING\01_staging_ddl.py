#!/usr/bin/env python3
"""
 RECONSTRUCTION SCHEMA STAGING AVEC TYPES EXACTS
PHASE 1 de l'ETL: Creation des tables staging avec types PostgreSQL EXACTS
Base sur l'analyse des 33 tables critiques Jira

 ATTENTION: Types de colonnes a 1000% EXACTS avec la source !
"""

import psycopg2
import logging
from datetime import datetime
import json
import sys
import os

# Add utilities path for config manager
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '05_UTILITIES'))
from config_manager import ETLConfigManager

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'staging_rebuild_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class StagingSchemaBuilder:
    """
    Construction du schema STAGING avec types PostgreSQL EXACTS
    """
    
    def __init__(self):
        # Load configuration from config manager
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        config_file = os.path.join(parent_dir, 'etl_config.json')
        self.config_manager = ETLConfigManager(config_file)

        # Get database configurations
        db_config = self.config_manager.config["database"]
        jira_config = self.config_manager.config["jira_instance"]

        # Configuration Data Warehouse
        self.dw_config = {
            'host': db_config['host'],
            'port': db_config['port'],
            'database': db_config['target_db'],
            'user': db_config['user'],
            'password': db_config['password']
        }

        # Configuration Jira (source)
        self.jira_config = {
            'host': jira_config['host'],
            'port': jira_config['port'],
            'database': jira_config['database'],
            'user': jira_config['user'],
            'password': jira_config['password']
        }
        
        # 33 tables fonctionnelles avec donnees (user specified list)
        self.all_critical_tables = [
            #  CORE_BUSINESS (9 tables)
            'jiraissue', 'project', 'component',
            'customfield', 'customfieldvalue', 'worklog', 'fileattachment',
            'issuelinktype', 'label',

            #  USERS_GROUPS (4 tables)
            'cwd_group', 'cwd_user', 'cwd_membership', 'projectroleactor',

            #  WORKFLOWS (3 tables)
            'jiraworkflows', 'workflowscheme', 'workflowschemeentity',

            #  CONFIGURATION (6 tables)
            'fieldconfigscheme', 'fieldconfiguration', 'fieldscreen', 'fieldscreentab',
            'permissionscheme', 'schemepermissions',

            #  LOOKUP_TABLES (7 tables)
            'priority', 'issuestatus', 'resolution', 'issuetype', 'projectrole',
            'pluginversion', 'managedconfigurationitem',

            #  AGILE_BOARDS (1 table)
            'AO_60DB71_RAPIDVIEW',

            #  JSM_AUDIT (3 tables)
            'AO_C77861_AUDIT_ENTITY', 'AO_C77861_AUDIT_ACTION_CACHE',
            'AO_C77861_AUDIT_CATEGORY_CACHE'
        ]

        # Tables prioritaires deja traitees (phase 1)
        self.phase1_tables = [
            'jiraissue', 'project', 'cwd_user', 'worklog', 'component',
            'customfield', 'customfieldvalue', 'changegroup', 'changeitem',
            'issuestatus', 'priority', 'resolution', 'issuetype'
        ]

        # Tables restantes a traiter (phase 2)
        self.phase2_tables = [table for table in self.all_critical_tables
                             if table not in self.phase1_tables]
        
        self.stats = {
            'start_time': datetime.now(),
            'tables_created': 0,
            'total_columns': 0,
            'errors': []
        }
    
    def connect_dw(self):
        """Connexion au Data Warehouse"""
        try:
            conn = psycopg2.connect(**self.dw_config)
            conn.autocommit = False
            return conn
        except Exception as e:
            logger.error(f" Erreur connexion DW: {e}")
            raise
    
    def connect_jira(self):
        """Connexion READ-ONLY a Jira"""
        try:
            conn = psycopg2.connect(**self.jira_config)
            conn.set_session(readonly=True, autocommit=True)
            return conn
        except Exception as e:
            logger.error(f" Erreur connexion Jira: {e}")
            raise
    
    def get_exact_column_types_from_jira(self, jira_cursor, table_name):
        """
        Recuperer les types de colonnes EXACTS directement depuis Jira
        Pour les tables non presentes dans TABLE_SCHEMAS_EXACTS
        """
        try:
            jira_cursor.execute("""
                SELECT
                    column_name,
                    data_type,
                    character_maximum_length,
                    numeric_precision,
                    numeric_scale,
                    is_nullable,
                    column_default,
                    ordinal_position
                FROM information_schema.columns
                WHERE table_schema = 'public' AND table_name = %s
                ORDER BY ordinal_position
            """, (table_name,))

            columns = jira_cursor.fetchall()

            if not columns:
                logger.warning(f" Aucune colonne trouvee pour {table_name}")
                return None

            # Construire le DDL exact
            column_definitions = []

            for col_name, data_type, char_max_len, num_precision, num_scale, is_nullable, col_default, position in columns:
                # Construire le type exact
                if data_type == 'character varying':
                    if char_max_len:
                        pg_type = f'VARCHAR({char_max_len})'
                    else:
                        pg_type = 'TEXT'
                elif data_type == 'character':
                    if char_max_len:
                        pg_type = f'CHAR({char_max_len})'
                    else:
                        pg_type = 'CHAR(1)'
                elif data_type == 'text':
                    pg_type = 'TEXT'
                elif data_type == 'numeric':
                    if num_precision and num_scale is not None:
                        pg_type = f'NUMERIC({num_precision},{num_scale})'
                    elif num_precision:
                        pg_type = f'NUMERIC({num_precision})'
                    else:
                        pg_type = 'NUMERIC'
                elif data_type == 'integer':
                    pg_type = 'INTEGER'
                elif data_type == 'bigint':
                    pg_type = 'BIGINT'
                elif data_type == 'smallint':
                    pg_type = 'SMALLINT'
                elif data_type == 'double precision':
                    pg_type = 'DOUBLE PRECISION'
                elif data_type == 'real':
                    pg_type = 'REAL'
                elif data_type == 'timestamp with time zone':
                    pg_type = 'TIMESTAMPTZ'
                elif data_type == 'timestamp without time zone':
                    pg_type = 'TIMESTAMP'
                elif data_type == 'date':
                    pg_type = 'DATE'
                elif data_type == 'time with time zone':
                    pg_type = 'TIMETZ'
                elif data_type == 'time without time zone':
                    pg_type = 'TIME'
                elif data_type == 'boolean':
                    pg_type = 'BOOLEAN'
                elif data_type == 'bytea':
                    pg_type = 'BYTEA'
                else:
                    # Type non reconnu, utiliser TEXT par securite
                    pg_type = 'TEXT'
                    logger.warning(f" Type non reconnu '{data_type}' pour {table_name}.{col_name}, utilisation TEXT")

                # Contrainte NULL/NOT NULL
                null_constraint = '' if is_nullable == 'YES' else ' NOT NULL'

                # Definition complete de la colonne
                column_def = f'    "{col_name}" {pg_type}{null_constraint}'
                column_definitions.append(column_def)

            logger.info(f"    {table_name}: {len(column_definitions)} colonnes depuis Jira")
            return column_definitions

        except Exception as e:
            logger.error(f" Erreur recuperation types pour {table_name}: {e}")
            return None

    def get_exact_column_types_from_schemas(self, table_name):
        """
        Recuperer les types de colonnes EXACTS depuis les schemas pre-analyses
        """
        # Import des schemas EXACTS depuis example/extract.py
        import sys
        import os

        try:
            # Ajouter le chemin vers example/
            example_path = os.path.join(os.path.dirname(__file__), 'example')
            if example_path not in sys.path:
                sys.path.insert(0, example_path)

            import extract
            TABLE_SCHEMAS_EXACTS = extract.TABLE_SCHEMAS_EXACTS

            if table_name in TABLE_SCHEMAS_EXACTS:
                schema_ddl = TABLE_SCHEMAS_EXACTS[table_name]

                # Parser le DDL pour extraire les definitions de colonnes
                lines = [line.strip() for line in schema_ddl.strip().split('\n') if line.strip()]
                column_definitions = []

                for line in lines:
                    # Ignorer les lignes speciales
                    if line.startswith('PRIMARY KEY') or line.startswith('instance_id') or line.startswith('extracted_at'):
                        continue

                    # Nettoyer la ligne (enlever virgules finales)
                    clean_line = line.rstrip(',')
                    if clean_line and not clean_line.startswith('PRIMARY'):
                        column_definitions.append(f'    {clean_line}')

                logger.info(f"    {table_name}: {len(column_definitions)} colonnes depuis schemas EXACTS")
                return column_definitions

            else:
                return None  # Table non trouvee dans schemas pre-analyses

        except Exception as e:
            logger.error(f" Erreur recuperation schemas EXACTS pour {table_name}: {e}")
            return None
    
    def create_staging_schema(self):
        """Creer le schema staging"""
        logger.info(" Creation du schema STAGING")
        
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            # Creer le schema staging
            cursor.execute('CREATE SCHEMA IF NOT EXISTS staging')
            dw_conn.commit()
            logger.info(" Schema staging cree")
            
        except Exception as e:
            logger.error(f" Erreur creation schema staging: {e}")
            dw_conn.rollback()
            raise
        finally:
            dw_conn.close()
    
    def create_staging_table(self, table_name, column_definitions):
        """
        Creer une table staging avec types EXACTS + colonnes ETL
        """
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            # Ajouter les colonnes ETL standard
            etl_columns = [
                '    instance_id INTEGER NOT NULL',
                '    extracted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
            ]
            
            all_columns = column_definitions + etl_columns
            columns_ddl = ',\n'.join(all_columns)
            
            # Creer la table
            create_sql = f'''
                CREATE TABLE staging."{table_name}" (
                {columns_ddl}
                )
            '''
            
            cursor.execute(f'DROP TABLE IF EXISTS staging."{table_name}" CASCADE')
            cursor.execute(create_sql)
            dw_conn.commit()
            
            self.stats['tables_created'] += 1
            self.stats['total_columns'] += len(column_definitions)
            
            logger.info(f"    staging.{table_name}: {len(column_definitions)} colonnes + 2 ETL")
            
        except Exception as e:
            logger.error(f"    Erreur creation staging.{table_name}: {e}")
            dw_conn.rollback()
            self.stats['errors'].append(f"{table_name}: {e}")
            raise
        finally:
            dw_conn.close()
    
    def build_all_staging_tables(self):
        """
        Construire toutes les tables staging (33 tables fonctionnelles)
        """
        logger.info(" CONSTRUCTION STAGING COMPLETE - 33 TABLES FONCTIONNELLES")
        logger.info("=" * 70)

        # Connect to Jira for column introspection
        jira_conn = self.connect_jira()
        jira_cursor = jira_conn.cursor()

        try:
            for i, table_name in enumerate(self.all_critical_tables, 1):
                logger.info(f" [{i:2d}/33] Creation staging.{table_name}")

                # Try to get column definitions from Jira directly
                column_definitions = self.get_exact_column_types_from_jira(jira_cursor, table_name)

                if column_definitions:
                    # Creer la table staging
                    self.create_staging_table(table_name, column_definitions)
                else:
                    logger.warning(f"    Table {table_name} non trouvee dans Jira - ignoree")
                    self.stats['errors'].append(f"{table_name}: Table non trouvee dans Jira")
        finally:
            jira_conn.close()


    
    def verify_staging_schema(self):
        """Verifier la creation du schema staging"""
        logger.info("\n VERIFICATION SCHEMA STAGING")
        logger.info("=" * 50)
        
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            # Lister les tables creees
            cursor.execute("""
                SELECT table_name, 
                       (SELECT COUNT(*) FROM information_schema.columns 
                        WHERE table_schema = 'staging' AND table_name = t.table_name) as column_count
                FROM information_schema.tables t
                WHERE table_schema = 'staging'
                ORDER BY table_name
            """)
            
            staging_tables = cursor.fetchall()
            
            logger.info(f" Tables staging creees: {len(staging_tables)}")
            
            for table_name, column_count in staging_tables:
                logger.info(f"    staging.{table_name}: {column_count} colonnes")
            
            return len(staging_tables)
            
        finally:
            dw_conn.close()

def main():
    """Point d'entree principal"""
    print("RECONSTRUCTION SCHEMA STAGING COMPLET")
    print("=" * 60)
    print("33 tables fonctionnelles avec types EXACTS")
    print("Schema: staging.*")
    print("=" * 60)

    builder = StagingSchemaBuilder()

    try:
        # Creer le schema staging
        builder.create_staging_schema()

        # Construire toutes les tables (33 tables)
        builder.build_all_staging_tables()

        # Verifier la creation
        total_tables = builder.verify_staging_schema()

        # Statistiques finales
        duration = (datetime.now() - builder.stats['start_time']).total_seconds()

        logger.info(f"\nSCHEMA STAGING CREE!")
        logger.info(f"Duree: {duration:.2f} secondes")
        logger.info(f"Tables creees: {builder.stats['tables_created']}")
        logger.info(f"Colonnes totales: {builder.stats['total_columns']}")
        logger.info(f"Erreurs: {len(builder.stats['errors'])}")

        if builder.stats['errors']:
            logger.warning(f"Erreurs rencontrees:")
            for error in builder.stats['errors'][:5]:
                logger.warning(f"   - {error}")

        logger.info(f"\nSTAGING COMPLET!")
        logger.info(f"   {total_tables}/33 tables staging disponibles")
        logger.info(f"\nPROCHAINE ETAPE:")
        logger.info(f"   Executer: python 02_staging_insert.py")
        logger.info(f"   Pour extraire les donnees vers staging")

    except Exception as e:
        logger.error(f" Erreur fatale: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
