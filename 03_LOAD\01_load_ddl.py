#!/usr/bin/env python3
"""
FINAL LOAD DDL - CONSTELLATION SCHEMA
Creates ONLY the tables you specified with:
- 5 fact tables
- 27 dimension tables
- All required foreign keys
- No additional dimensions
"""

import psycopg2
import logging
import sys
import os
from datetime import datetime

# Add utilities directory to path
utilities_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '05_UTILITIES')
sys.path.append(utilities_path)
from config_manager import ETLConfigManager

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    handlers=[
        logging.FileHandler('load_ddl.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

DDL_STATEMENTS = [
    # 1. Drop and create schema
    "DROP SCHEMA IF EXISTS load CASCADE;",
    "CREATE SCHEMA load;",

    # 2. Dimension Tables (27 tables as per your requirements)
    '''CREATE TABLE load.dim_date (
        date_id DATE PRIMARY KEY,
        year INTEGER NOT NULL,
        quarter INTEGER NOT NULL,
        month INTEGER NOT NULL,
        day INTEGER NOT NULL,
        week INTEGER NOT NULL,
        day_of_week INTEGER NOT NULL,
        day_name VARCHAR(9) NOT NULL,
        month_name VARCHAR(9) NOT NULL,
        is_weekend BOOLEAN NOT NULL
    );''',

    '''CREATE TABLE load.dim_cwd_user (
	user_name varchar(255) NULL,
	active numeric(9) NULL,
	created_date timestamptz NULL,
	updated_date timestamptz NULL,
	email_address varchar(255) NULL,
	instance_id int4 NOT NULL,
	transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL
    );''',

    '''CREATE TABLE load.dim_cwd_group (
        group_id NUMERIC(18) PRIMARY KEY,
        group_name VARCHAR(255) NOT NULL,
        active BOOLEAN DEFAULT TRUE
    );''',

    '''CREATE TABLE load.dim_project (
        project_id NUMERIC(18) PRIMARY KEY,
        project_key VARCHAR(10) NOT NULL,
        project_name VARCHAR(255) NOT NULL,
        project_lead_id NUMERIC(18),
        FOREIGN KEY (project_lead_id) REFERENCES load.dim_cwd_user(user_id)
    );''',

    '''CREATE TABLE load.dim_component (
        component_id NUMERIC(18) PRIMARY KEY,
        project_id NUMERIC(18) NOT NULL,
        component_name VARCHAR(255) NOT NULL,
        FOREIGN KEY (project_id) REFERENCES load.dim_project(project_id)
    );''',

    '''CREATE TABLE load.dim_issuetype (
        issuetype_id NUMERIC(18) PRIMARY KEY,
        issuetype_name VARCHAR(255) NOT NULL,
        subtask BOOLEAN DEFAULT FALSE
    );''',

    '''CREATE TABLE load.dim_priority (
        priority_id NUMERIC(18) PRIMARY KEY,
        priority_name VARCHAR(255) NOT NULL
    );''',

    '''CREATE TABLE load.dim_resolution (
        resolution_id NUMERIC(18) PRIMARY KEY,
        resolution_name VARCHAR(255) NOT NULL
    );''',

    '''CREATE TABLE load.dim_issuestatus (
        status_id NUMERIC(18) PRIMARY KEY,
        status_name VARCHAR(255) NOT NULL
    );''',

    '''CREATE TABLE load.dim_customfield (
        customfield_id NUMERIC(18) PRIMARY KEY,
        customfield_name VARCHAR(255) NOT NULL,
        customfield_type VARCHAR(100) NOT NULL
    );''',

    '''CREATE TABLE load.dim_label (
        label_id NUMERIC(18) PRIMARY KEY,
        label_name VARCHAR(255) NOT NULL
    );''',

    '''CREATE TABLE load.dim_ao_60db71_rapidview (
        rapidview_id NUMERIC(18) PRIMARY KEY,
        rapidview_name VARCHAR(255) NOT NULL,
        owner_user_name VARCHAR(255) NOT NULL
    );''',

    '''CREATE TABLE load.dim_ao_c77861_audit_entity (
        audit_entity_id NUMERIC(18) PRIMARY KEY,
        user_id NUMERIC(18),
        action VARCHAR(255) NOT NULL,
        entity_type VARCHAR(255) NOT NULL,
        FOREIGN KEY (user_id) REFERENCES load.dim_cwd_user(user_id)
    );''',

    '''CREATE TABLE load.dim_ao_c77861_audit_action_cache (
        action_cache_id NUMERIC(18) PRIMARY KEY,
        action_name VARCHAR(255) NOT NULL
    );''',

    '''CREATE TABLE load.dim_ao_c77861_audit_category_cache (
        category_cache_id NUMERIC(18) PRIMARY KEY,
        category_name VARCHAR(255) NOT NULL
    );''',

    '''CREATE TABLE load.dim_jiraworkflows (
        workflow_id NUMERIC(18) PRIMARY KEY,
        workflow_name VARCHAR(255) NOT NULL
    );''',

    '''CREATE TABLE load.dim_workflowscheme (
        scheme_id NUMERIC(18) PRIMARY KEY,
        scheme_name VARCHAR(255) NOT NULL
    );''',

    '''CREATE TABLE load.dim_fieldconfigscheme (
        fieldconfigscheme_id NUMERIC(18) PRIMARY KEY,
        scheme_name VARCHAR(255) NOT NULL
    );''',

    '''CREATE TABLE load.dim_fieldconfiguration (
        fieldconfig_id NUMERIC(18) PRIMARY KEY,
        config_name VARCHAR(255) NOT NULL
    );''',

    '''CREATE TABLE load.dim_fieldscreen (
        fieldscreen_id NUMERIC(18) PRIMARY KEY,
        screen_name VARCHAR(255) NOT NULL
    );''',

    '''CREATE TABLE load.dim_fieldscreentab (
        screentab_id NUMERIC(18) PRIMARY KEY,
        tab_name VARCHAR(255) NOT NULL
    );''',

    '''CREATE TABLE load.dim_permissionscheme (
        permissionscheme_id NUMERIC(18) PRIMARY KEY,
        scheme_name VARCHAR(255) NOT NULL
    );''',

    '''CREATE TABLE load.dim_schemepermissions (
        schemepermission_id NUMERIC(18) PRIMARY KEY,
        permission_name VARCHAR(255) NOT NULL
    );''',

    '''CREATE TABLE load.dim_pluginversion (
        pluginversion_id NUMERIC(18) PRIMARY KEY,
        version_name VARCHAR(255) NOT NULL
    );''',

    '''CREATE TABLE load.dim_managedconfigurationitem (
        manageditem_id NUMERIC(18) PRIMARY KEY,
        item_type VARCHAR(255) NOT NULL
    );''',

    '''CREATE TABLE load.dim_projectrole (
        projectrole_id NUMERIC(18) PRIMARY KEY,
        role_name VARCHAR(255) NOT NULL
    );''',

    '''CREATE TABLE load.dim_projectroleactor (
        projectroleactor_id NUMERIC(18) PRIMARY KEY,
        actor_type VARCHAR(255) NOT NULL
    );''',

    '''CREATE TABLE load.dim_cwd_membership (
        membership_id NUMERIC(18) PRIMARY KEY,
        membership_type VARCHAR(255) NOT NULL
    );''',

    # 3. Fact Tables (5 tables as per your requirements)
    '''CREATE TABLE load.fact_jiraissue (
        issue_id NUMERIC(18) PRIMARY KEY,
        issue_key VARCHAR(50) NOT NULL,
        project_id NUMERIC(18) NOT NULL,
        reporter_id NUMERIC(18),
        assignee_id NUMERIC(18),
        creator_id NUMERIC(18),
        issuetype_id NUMERIC(18) NOT NULL,
        priority_id NUMERIC(18),
        status_id NUMERIC(18) NOT NULL,
        resolution_id NUMERIC(18),
        component_id NUMERIC(18),
        created_date DATE NOT NULL,
        updated_date DATE NOT NULL,
        resolution_date DATE,
        time_spent NUMERIC(18),
        time_estimate NUMERIC(18),
        story_points NUMERIC(18,2),
        summary TEXT,
        FOREIGN KEY (project_id) REFERENCES load.dim_project(project_id),
        FOREIGN KEY (reporter_id) REFERENCES load.dim_cwd_user(user_id),
        FOREIGN KEY (assignee_id) REFERENCES load.dim_cwd_user(user_id),
        FOREIGN KEY (creator_id) REFERENCES load.dim_cwd_user(user_id),
        FOREIGN KEY (issuetype_id) REFERENCES load.dim_issuetype(issuetype_id),
        FOREIGN KEY (priority_id) REFERENCES load.dim_priority(priority_id),
        FOREIGN KEY (status_id) REFERENCES load.dim_issuestatus(status_id),
        FOREIGN KEY (resolution_id) REFERENCES load.dim_resolution(resolution_id),
        FOREIGN KEY (component_id) REFERENCES load.dim_component(component_id),
        FOREIGN KEY (created_date) REFERENCES load.dim_date(date_id),
        FOREIGN KEY (updated_date) REFERENCES load.dim_date(date_id),
        FOREIGN KEY (resolution_date) REFERENCES load.dim_date(date_id)
    );''',

    '''CREATE TABLE load.fact_worklog (
        worklog_id NUMERIC(18) PRIMARY KEY,
        issue_id NUMERIC(18) NOT NULL,
        author_id NUMERIC(18) NOT NULL,
        worklog_date DATE NOT NULL,
        time_spent NUMERIC(18) NOT NULL,
        comment TEXT,
        FOREIGN KEY (issue_id) REFERENCES load.fact_jiraissue(issue_id),
        FOREIGN KEY (author_id) REFERENCES load.dim_cwd_user(user_id),
        FOREIGN KEY (worklog_date) REFERENCES load.dim_date(date_id)
    );''',

    '''CREATE TABLE load.fact_customfieldvalue (
        customfieldvalue_id NUMERIC(18) PRIMARY KEY,
        issue_id NUMERIC(18) NOT NULL,
        customfield_id NUMERIC(18) NOT NULL,
        string_value VARCHAR(255),
        number_value NUMERIC(18,2),
        text_value TEXT,
        date_value DATE,
        FOREIGN KEY (issue_id) REFERENCES load.fact_jiraissue(issue_id),
        FOREIGN KEY (customfield_id) REFERENCES load.dim_customfield(customfield_id),
        FOREIGN KEY (date_value) REFERENCES load.dim_date(date_id)
    );''',

    '''CREATE TABLE load.fact_changelog (
        change_id NUMERIC(18) PRIMARY KEY,
        issue_id NUMERIC(18) NOT NULL,
        author_id NUMERIC(18),
        change_date DATE NOT NULL,
        field_changed VARCHAR(255) NOT NULL,
        old_value TEXT,
        new_value TEXT,
        FOREIGN KEY (issue_id) REFERENCES load.fact_jiraissue(issue_id),
        FOREIGN KEY (author_id) REFERENCES load.dim_cwd_user(user_id),
        FOREIGN KEY (change_date) REFERENCES load.dim_date(date_id)
    );''',

    '''CREATE TABLE load.fact_sprint_issue (
        sprint_issue_id NUMERIC(18) PRIMARY KEY,
        sprint_id NUMERIC(18) NOT NULL,
        issue_id NUMERIC(18) NOT NULL,
        added_date DATE,
        removed_date DATE,
        FOREIGN KEY (sprint_id) REFERENCES load.dim_ao_60db71_rapidview(rapidview_id),
        FOREIGN KEY (issue_id) REFERENCES load.fact_jiraissue(issue_id),
        FOREIGN KEY (added_date) REFERENCES load.dim_date(date_id),
        FOREIGN KEY (removed_date) REFERENCES load.dim_date(date_id)
    );''',

    # 4. Indexes for performance
    '''CREATE INDEX idx_fact_jiraissue_project ON load.fact_jiraissue(project_id);''',
    '''CREATE INDEX idx_fact_jiraissue_status ON load.fact_jiraissue(status_id);''',
    '''CREATE INDEX idx_fact_worklog_issue ON load.fact_worklog(issue_id);''',
    '''CREATE INDEX idx_fact_customfieldvalue_issue ON load.fact_customfieldvalue(issue_id);''',
    '''CREATE INDEX idx_fact_changelog_issue ON load.fact_changelog(issue_id);'''
]

class LoadDDLBuilder:
    def __init__(self):
        try:
            parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            config_file = os.path.join(parent_dir, 'etl_config.json')
            self.config_manager = ETLConfigManager(config_file)
            db_config = self.config_manager.config["database"]
            self.dw_config = {
                'host': db_config['host'],
                'port': db_config['port'],
                'database': db_config['target_db'],
                'user': db_config['user'],
                'password': db_config['password']
            }
        except Exception as e:
            logger.error(f"Config load failed: {e}")
            raise

    def connect_dw(self):
        try:
            return psycopg2.connect(**self.dw_config)
        except Exception as e:
            logger.error(f"DW connection error: {e}")
            raise

    def run_ddl(self):
        conn = self.connect_dw()
        try:
            with conn.cursor() as cursor:
                for stmt in DDL_STATEMENTS:
                    try:
                        cursor.execute(stmt)
                    except Exception as e:
                        logger.error(f"Error executing: {stmt[:100]}... Error: {e}")
                        conn.rollback()
                        raise
                conn.commit()
                logger.info("All DDL statements executed successfully")
        finally:
            conn.close()

if __name__ == "__main__":
    logger.info("Starting Load DDL Execution")
    try:
        builder = LoadDDLBuilder()
        builder.run_ddl()
        logger.info("DDL completed successfully")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)