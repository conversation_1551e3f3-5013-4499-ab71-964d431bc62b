#!/usr/bin/env python3
"""
COMPLETE CONSTELLATION SCHEMA - ALL 33 JIRA TABLES
Creates comprehensive constellation schema with:
- 6 fact tables (jiraissue, worklog, customfieldvalue, fileattachment, audit_entity, changelog)
- 27 dimension tables (all remaining tables)
- Proper Jira relationships and foreign keys
- Optimized for Jira Data Center structure
"""

import psycopg2
import logging
import sys
import os
from datetime import datetime

# Add utilities directory to path
utilities_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '05_UTILITIES')
sys.path.append(utilities_path)
from config_manager import ETLConfigManager

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    handlers=[
        logging.FileHandler('load_ddl.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

DDL_STATEMENTS = [
    # 1. Drop and create schema
    "DROP SCHEMA IF EXISTS load CASCADE;",
    "CREATE SCHEMA load;",

    # 2. TIME DIMENSION - Foundation for all temporal analysis
    '''CREATE TABLE load.dim_date (
        date_id DATE PRIMARY KEY,
        year INTEGER NOT NULL,
        quarter INTEGER NOT NULL,
        month INTEGER NOT NULL,
        day INTEGER NOT NULL,
        week INTEGER NOT NULL,
        day_of_week INTEGER NOT NULL,
        day_name VARCHAR(9) NOT NULL,
        month_name VARCHAR(9) NOT NULL,
        is_weekend BOOLEAN NOT NULL,
        fiscal_year INTEGER,
        fiscal_quarter INTEGER
    );''',

    # 3. USER & GROUP DIMENSIONS - Core identity management
    '''CREATE TABLE load.dim_cwd_user (
        id NUMERIC(18) PRIMARY KEY,
        user_name VARCHAR(255) UNIQUE NOT NULL,
        display_name VARCHAR(255),
        email_address VARCHAR(255),
        active NUMERIC(9),
        created_date TIMESTAMPTZ,
        updated_date TIMESTAMPTZ,
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_cwd_group (
        id NUMERIC(18) PRIMARY KEY,
        group_name VARCHAR(255) NOT NULL,
        lower_group_name VARCHAR(255) NOT NULL,
        active NUMERIC(9),
        created_date TIMESTAMPTZ,
        updated_date TIMESTAMPTZ,
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_cwd_membership (
        id NUMERIC(18) PRIMARY KEY,
        parent_id NUMERIC(18),
        child_id NUMERIC(18),
        membership_type VARCHAR(60),
        parent_name VARCHAR(255) NULL,
        lower_parent_name VARCHAR(255) NULL,
        child_name VARCHAR(255) NULL,
        lower_child_name VARCHAR(255) NULL,
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES load.dim_cwd_group(id),
        FOREIGN KEY (child_id) REFERENCES load.dim_cwd_user(id)
    );''',

    # 4. PROJECT & COMPONENT DIMENSIONS - Core business structure
    '''CREATE TABLE load.dim_project (
        id NUMERIC(18) PRIMARY KEY,
        pkey VARCHAR(255) NOT NULL,
        pname VARCHAR(255) NOT NULL,
        lead NUMERIC(18) NULL,
        pcounter NUMERIC(18) NULL,
        assigneetype NUMERIC(18) NULL,
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (lead) REFERENCES load.dim_cwd_user(id)
    );''',

    '''CREATE TABLE load.dim_component (
        id NUMERIC(18) PRIMARY KEY,
        project NUMERIC(18) NOT NULL,
        cname VARCHAR(255) NOT NULL,
        archived VARCHAR(255) NOT NULL,
        deleted VARCHAR(255) NOT NULL,
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project) REFERENCES load.dim_project(id)
    );''',

    # 5. ISSUE TYPE & STATUS DIMENSIONS - Core issue classification
    '''CREATE TABLE load.dim_issuetype (
        id NUMERIC(18) PRIMARY KEY,
        pname VARCHAR(60) NOT NULL,
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_priority (
        id NUMERIC(18) PRIMARY KEY,
        pname VARCHAR(255) NOT NULL,
        status_color VARCHAR(7),
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_resolution (
        id NUMERIC(18) PRIMARY KEY,
        pname VARCHAR(60) NOT NULL,
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_issuestatus (
        id NUMERIC(18) PRIMARY KEY,
        pname VARCHAR(60) NOT NULL,
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    # 6. CUSTOM FIELDS & LABELS - Flexible content
    '''CREATE TABLE load.dim_customfield (
        id NUMERIC(18) PRIMARY KEY,
        customfieldtypekey VARCHAR(255)  NULL,
        customfieldsearcherkey VARCHAR(255)  NULL,
        cfname VARCHAR(255) NOT NULL,
        project NUMERIC(18) NULL,
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_label (
        id NUMERIC(18) PRIMARY KEY,
        issue NUMERIC (18) NULL,
        "label" VARCHAR(255) NOT NULL,
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_issuelinktype (
        id NUMERIC(18) PRIMARY KEY,
        linkname VARCHAR(255) NULL,
        inward VARCHAR(255) NULL,
        outward VARCHAR(255) NULL,
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    # 7. WORKFLOW DIMENSIONS - Process management
    '''CREATE TABLE load.dim_jiraworkflows (
        id NUMERIC(18) PRIMARY KEY,
        workflowname VARCHAR(255) NULL,
        "descriptor" TEXT NULL,
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_workflowscheme (
        id NUMERIC(18) PRIMARY KEY,
        "name" VARCHAR(255) NOT NULL,
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_workflowschemeentity (
        id NUMERIC(18) PRIMARY KEY,
        scheme NUMERIC(18),
        workflow NUMERIC(18),
        issuetype NUMERIC(18),
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (scheme) REFERENCES load.dim_workflowscheme(id),
        FOREIGN KEY (workflow) REFERENCES load.dim_jiraworkflows(id),
        FOREIGN KEY (issuetype) REFERENCES load.dim_issuetype(id)
    );''',

    # 8. FIELD CONFIGURATION DIMENSIONS - UI management
    '''CREATE TABLE load.dim_fieldconfigscheme (
        id NUMERIC(18) PRIMARY KEY,
        configname VARCHAR(255) NOT NULL,
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_fieldconfiguration (
        id NUMERIC(18) PRIMARY KEY,
        configname VARCHAR(255) NOT NULL,
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_fieldscreen (
        id NUMERIC(18) PRIMARY KEY,
        "name" VARCHAR(255) NOT NULL,
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_fieldscreentab (
        id NUMERIC(18) PRIMARY KEY,
        "name" VARCHAR(255)  NULL,
        fieldscreen NUMERIC(18),
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (fieldscreen) REFERENCES load.dim_fieldscreen(id)
    );''',

    # 9. PERMISSION & SECURITY DIMENSIONS
    '''CREATE TABLE load.dim_permissionscheme (
        id NUMERIC(18) PRIMARY KEY,
        "name" VARCHAR(255) NULL,
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_schemepermissions (
        id NUMERIC(18) PRIMARY KEY,
        scheme NUMERIC(18),
        permission NUMERIC(18) NULL,
        perm_type VARCHAR(255) NULL,
        perm_parameter VARCHAR(255) NULL,
        permission_key VARCHAR(255) NULL,
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (scheme) REFERENCES load.dim_permissionscheme(id)
    );''',

    '''CREATE TABLE load.dim_projectrole (
        id NUMERIC(18) PRIMARY KEY,
        "name" VARCHAR(255) NULL,
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_projectroleactor (
        id NUMERIC(18) PRIMARY KEY,
        pid NUMERIC(18) NULL,
        projectroleid NUMERIC(18) NULL,
        roletype VARCHAR(50) NULL,
        roletypeparameter VARCHAR(255),
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (pid) REFERENCES load.dim_project(id),
        FOREIGN KEY (projectroleid) REFERENCES load.dim_projectrole(id)
    );''',

    # 10. SYSTEM & PLUGIN DIMENSIONS
    '''CREATE TABLE load.dim_pluginversion (
        id NUMERIC(18) PRIMARY KEY,
        pluginkey VARCHAR(255) NULL,
        pluginname VARCHAR(255) NULL,
        pluginversion VARCHAR(255) NULL,
        created TIMESTAMPTZ NULL,
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_managedconfigurationitem (
        id NUMERIC(18) PRIMARY KEY,
        item_id NUMERIC(18) NULL,
        item_type VARCHAR(255) NULL,
        managed VARCHAR(255) NULL,
        access_level VARCHAR(255) NULL,
        source VARCHAR(255) NULL,
        description_key VARCHAR(255) NULL,
        instance_id int4 NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    # 11. AGILE & AUDIT DIMENSIONS (AO tables)
    '''CREATE TABLE load.dim_ao_60db71_rapidview (
        ID NUMERIC(18) PRIMARY KEY,
        CARD_COLOR_STRATEGY varchar(255) NULL,
        KAN_PLAN_ENABLED bool NULL,
        NAME varchar(255) NOT NULL,
        OLD_DONE_ISSUES_CUTOFF varchar(255) NULL,
        OWNER_USER_NAME varchar(255) NOT NULL,
        REFINED_VELOCITY_ACTIVE bool NULL,
        SAVED_FILTER_ID NUMERIC(18) NOT NULL,
        SHOW_DAYS_IN_COLUMN bool NULL,
        SHOW_EPIC_AS_PANEL bool NULL,
        SPRINTS_ENABLED bool NULL,
        SWIMLANE_STRATEGY varchar(255) NULL,
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_ao_c77861_audit_action_cache (
        ID NUMERIC(18) PRIMARY KEY,
        ACTION varchar(255) NOT NULL,
        ACTION_T_KEY varchar(255) NULL,
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_ao_c77861_audit_category_cache (
        ID NUMERIC(18) PRIMARY KEY,
        CATEGORY varchar(255) NOT NULL,
        CATEGORY_T_KEY varchar(255) NULL,
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    # ============================================================================
    # FACT TABLES - Core business events and transactions
    # ============================================================================

    # 1. MAIN FACT TABLE - Issues (Core business entity)
    '''CREATE TABLE load.fact_jiraissue (
        id NUMERIC(18) PRIMARY KEY,
        pkey VARCHAR(50) UNIQUE NOT NULL,
        project NUMERIC(18) NOT NULL,
        reporter NUMERIC(18),
        assignee NUMERIC(18),
        creator NUMERIC(18),
        issuetype NUMERIC(18) NOT NULL,
        priority NUMERIC(18),
        issuestatus NUMERIC(18) NOT NULL,
        resolution NUMERIC(18),
        created TIMESTAMPTZ,
        updated TIMESTAMPTZ,
        resolutiondate TIMESTAMPTZ,
        duedate DATE,
        timespent NUMERIC(18),
        timeestimate NUMERIC(18),
        timeoriginalestimate NUMERIC(18),
        summary TEXT NOT NULL,
        -- ETL columns
        instance_id INTEGER NOT NULL,
        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        -- Foreign Keys
        FOREIGN KEY (project) REFERENCES load.dim_project(id),
        FOREIGN KEY (reporter) REFERENCES load.dim_cwd_user(id),
        FOREIGN KEY (assignee) REFERENCES load.dim_cwd_user(id),
        FOREIGN KEY (creator) REFERENCES load.dim_cwd_user(id),
        FOREIGN KEY (issuetype) REFERENCES load.dim_issuetype(id),
        FOREIGN KEY (priority) REFERENCES load.dim_priority(id),
        FOREIGN KEY (issuestatus) REFERENCES load.dim_issuestatus(id),
        FOREIGN KEY (resolution) REFERENCES load.dim_resolution(id)
    );''',

    # 2. WORKLOG FACT TABLE - Time tracking events
    '''CREATE TABLE load.fact_worklog (
        id NUMERIC(18) PRIMARY KEY,
        issueid NUMERIC(18) NULL,
        author NUMERIC(18) NULL,
        grouplevel VARCHAR(255),
        rolelevel VARCHAR(255),
        worklogbody TEXT,
        created TIMESTAMPTZ,
        updateauthor NUMERIC(18),
        updated TIMESTAMPTZ,
        startdate TIMESTAMPTZ,
        timeworked NUMERIC(18) NOT NULL,
        -- ETL columns
        instance_id int4 NOT NULL,
        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        -- Foreign Keys
        FOREIGN KEY (issueid) REFERENCES load.fact_jiraissue(id),
        FOREIGN KEY (author) REFERENCES load.dim_cwd_user(id),
        FOREIGN KEY (updateauthor) REFERENCES load.dim_cwd_user(id)
    );''',

    # 3. CUSTOM FIELD VALUES FACT TABLE - Flexible data storage
    '''CREATE TABLE load.fact_customfieldvalue (
        id NUMERIC(18) PRIMARY KEY,
        issue NUMERIC(18) NULL,
        customfield NUMERIC(18) NULL,
        stringvalue VARCHAR(255) NULL,
        numbervalue NUMERIC(18,2) NULL,
        textvalue TEXT NULL,
        -- ETL columns
        instance_id int4 NOT NULL,
        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        -- Foreign Keys
        FOREIGN KEY (issue) REFERENCES load.fact_jiraissue(id),
        FOREIGN KEY (customfield) REFERENCES load.dim_customfield(id)
    );''',

    # 4. FILE ATTACHMENT FACT TABLE - Document management
    '''CREATE TABLE load.fact_fileattachment (
        id NUMERIC(18) PRIMARY KEY,
        issueid NUMERIC(18) NULL,
        author NUMERIC(18),
        filename VARCHAR(255) NULL,
        filesize NUMERIC(18),
        mimetype VARCHAR(100),
        created TIMESTAMPTZ,
        -- ETL columns
        instance_id int4 NOT NULL,
        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        -- Foreign Keys
        FOREIGN KEY (issueid) REFERENCES load.fact_jiraissue(id),
        FOREIGN KEY (author) REFERENCES load.dim_cwd_user(id)
    );''',

    # 5. AUDIT ENTITY FACT TABLE - Change tracking and audit trail
    '''CREATE TABLE load.fact_ao_c77861_audit_entity (
        ID NUMERIC(18) PRIMARY KEY,
        ACTION varchar(255) NOT NULL,
        ACTION_T_KEY varchar(255) NULL,
        AREA varchar(255) NOT NULL,
        ATTRIBUTES text NULL,
        CATEGORY varchar(255) NULL,
        CATEGORY_T_KEY varchar(255) NULL,
        CHANGE_VALUES text NULL,
        ENTITY_TIMESTAMP NUMERIC(18) NOT NULL,
        LEVEL varchar(255) NOT NULL,
        METHOD varchar(255) NULL,
        PRIMARY_RESOURCE_ID varchar(255) NULL,
        PRIMARY_RESOURCE_TYPE varchar(255) NULL,
        RESOURCES text NULL,
        SEARCH_STRING text NULL,
        SECONDARY_RESOURCE_ID varchar(255) NULL,
        SECONDARY_RESOURCE_TYPE varchar(255) NULL,
        SOURCE varchar(255) NULL,
        USER_ID varchar(255) NULL,
        USER_NAME varchar(255) NULL,
        USER_TYPE varchar(255) NULL,
        -- ETL columns
        instance_id INTEGER NOT NULL,
        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    # 6. AGILE BOARD FACT TABLE - Sprint and board activities (derived table)
    '''CREATE TABLE load.fact_sprint_issue (
        sprint_issue_id NUMERIC(18) PRIMARY KEY,
        rapidview_id NUMERIC(18) NOT NULL,
        issue_id NUMERIC(18) NOT NULL,
        sprint_id NUMERIC(18),
        added_date DATE,
        removed_date DATE,
        issue_rank NUMERIC(18),
        -- ETL columns
        instance_id INTEGER NOT NULL,
        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        -- Foreign Keys
        FOREIGN KEY (rapidview_id) REFERENCES load.dim_ao_60db71_rapidview(ID),
        FOREIGN KEY (issue_id) REFERENCES load.fact_jiraissue(id)
    );''',

    # ============================================================================
    # PERFORMANCE INDEXES - Optimized for constellation schema queries
    # ============================================================================

    # Core fact table indexes
    '''CREATE INDEX idx_fact_jiraissue_project ON load.fact_jiraissue(project);''',
    '''CREATE INDEX idx_fact_jiraissue_assignee ON load.fact_jiraissue(assignee);''',
    '''CREATE INDEX idx_fact_jiraissue_status ON load.fact_jiraissue(issuestatus);''',
    '''CREATE INDEX idx_fact_jiraissue_created ON load.fact_jiraissue(created);''',
    '''CREATE INDEX idx_fact_jiraissue_issuetype ON load.fact_jiraissue(issuetype);''',

    # Worklog fact indexes
    '''CREATE INDEX idx_fact_worklog_issue ON load.fact_worklog(issueid);''',
    '''CREATE INDEX idx_fact_worklog_author ON load.fact_worklog(author);''',
    '''CREATE INDEX idx_fact_worklog_created ON load.fact_worklog(created);''',

    # Custom field fact indexes
    '''CREATE INDEX idx_fact_customfieldvalue_issue ON load.fact_customfieldvalue(issue);''',
    '''CREATE INDEX idx_fact_customfieldvalue_field ON load.fact_customfieldvalue(customfield);''',

    # File attachment indexes
    '''CREATE INDEX idx_fact_fileattachment_issue ON load.fact_fileattachment(issueid);''',
    '''CREATE INDEX idx_fact_fileattachment_author ON load.fact_fileattachment(author);''',

    # Audit fact indexes
    '''CREATE INDEX idx_fact_audit_user ON load.fact_ao_c77861_audit_entity(USER_ID);''',
    '''CREATE INDEX idx_fact_audit_timestamp ON load.fact_ao_c77861_audit_entity(ENTITY_TIMESTAMP);''',
    '''CREATE INDEX idx_fact_audit_action ON load.fact_ao_c77861_audit_entity(ACTION);''',

    # Sprint fact indexes
    '''CREATE INDEX idx_fact_sprint_issue_board ON load.fact_sprint_issue(rapidview_id);''',
    '''CREATE INDEX idx_fact_sprint_issue_issue ON load.fact_sprint_issue(issue_id);'''
]

class LoadDDLBuilder:
    def __init__(self):
        try:
            parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            config_file = os.path.join(parent_dir, 'etl_config.json')
            self.config_manager = ETLConfigManager(config_file)
            db_config = self.config_manager.config["database"]
            self.dw_config = {
                'host': db_config['host'],
                'port': db_config['port'],
                'database': db_config['target_db'],
                'user': db_config['user'],
                'password': db_config['password']
            }
        except Exception as e:
            logger.error(f"Config load failed: {e}")
            raise

    def connect_dw(self):
        try:
            return psycopg2.connect(**self.dw_config)
        except Exception as e:
            logger.error(f"DW connection error: {e}")
            raise

    def run_ddl(self):
        conn = self.connect_dw()
        try:
            with conn.cursor() as cursor:
                for stmt in DDL_STATEMENTS:
                    try:
                        cursor.execute(stmt)
                    except Exception as e:
                        logger.error(f"Error executing: {stmt[:100]}... Error: {e}")
                        conn.rollback()
                        raise
                conn.commit()
                logger.info("All DDL statements executed successfully")
        finally:
            conn.close()

if __name__ == "__main__":
    logger.info("Starting Load DDL Execution")
    try:
        builder = LoadDDLBuilder()
        builder.run_ddl()
        logger.info("DDL completed successfully")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)