#!/usr/bin/env python3
"""
COMPLETE CONSTELLATION SCHEMA - ALL 33 JIRA TABLES
Creates comprehensive constellation schema with:
- 6 fact tables (jiraissue, worklog, customfieldvalue, fileattachment, audit_entity, changelog)
- 27 dimension tables (all remaining tables)
- Proper Jira relationships and foreign keys
- Optimized for Jira Data Center structure
"""

import psycopg2
import logging
import sys
import os
from datetime import datetime

# Add utilities directory to path
utilities_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '05_UTILITIES')
sys.path.append(utilities_path)
from config_manager import ETLConfigManager

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    handlers=[
        logging.FileHandler('load_ddl.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

DDL_STATEMENTS = [
    # 1. Drop and create schema
    "DROP SCHEMA IF EXISTS load CASCADE;",
    "CREATE SCHEMA load;",

    # 2. TIME DIMENSION - Foundation for all temporal analysis
    '''CREATE TABLE load.dim_date (
        date_id DATE PRIMARY KEY,
        year INTEGER NOT NULL,
        quarter INTEGER NOT NULL,
        month INTEGER NOT NULL,
        day INTEGER NOT NULL,
        week INTEGER NOT NULL,
        day_of_week INTEGER NOT NULL,
        day_name VARCHAR(9) NOT NULL,
        month_name VARCHAR(9) NOT NULL,
        is_weekend BOOLEAN NOT NULL,
        fiscal_year INTEGER,
        fiscal_quarter INTEGER
    );''',

    # 3. USER & GROUP DIMENSIONS - Core identity management
    '''CREATE TABLE load.dim_cwd_user (
        user_id NUMERIC(18) PRIMARY KEY,
        user_name VARCHAR(255) UNIQUE NOT NULL,
        display_name VARCHAR(255),
        email_address VARCHAR(255),
        active BOOLEAN DEFAULT TRUE,
        created_date TIMESTAMPTZ,
        updated_date TIMESTAMPTZ,
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_cwd_group (
        group_id NUMERIC(18) PRIMARY KEY,
        group_name VARCHAR(255) NOT NULL,
        description TEXT,
        active BOOLEAN DEFAULT TRUE,
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_cwd_membership (
        membership_id NUMERIC(18) PRIMARY KEY,
        user_id NUMERIC(18),
        group_id NUMERIC(18),
        membership_type VARCHAR(50),
        instance_id INTEGER NOT NULL,
        FOREIGN KEY (user_id) REFERENCES load.dim_cwd_user(user_id),
        FOREIGN KEY (group_id) REFERENCES load.dim_cwd_group(group_id)
    );''',

    # 4. PROJECT & COMPONENT DIMENSIONS - Core business structure
    '''CREATE TABLE load.dim_project (
        project_id NUMERIC(18) PRIMARY KEY,
        project_key VARCHAR(10) UNIQUE NOT NULL,
        project_name VARCHAR(255) NOT NULL,
        project_lead_id NUMERIC(18),
        project_category VARCHAR(255),
        project_type VARCHAR(50),
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_lead_id) REFERENCES load.dim_cwd_user(user_id)
    );''',

    '''CREATE TABLE load.dim_component (
        component_id NUMERIC(18) PRIMARY KEY,
        project_id NUMERIC(18) NOT NULL,
        component_name VARCHAR(255) NOT NULL,
        component_lead_id NUMERIC(18),
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES load.dim_project(project_id),
        FOREIGN KEY (component_lead_id) REFERENCES load.dim_cwd_user(user_id)
    );''',

    # 5. ISSUE TYPE & STATUS DIMENSIONS - Core issue classification
    '''CREATE TABLE load.dim_issuetype (
        issuetype_id NUMERIC(18) PRIMARY KEY,
        issuetype_name VARCHAR(255) NOT NULL,
        description TEXT,
        subtask BOOLEAN DEFAULT FALSE,
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_priority (
        priority_id NUMERIC(18) PRIMARY KEY,
        priority_name VARCHAR(255) NOT NULL,
        priority_color VARCHAR(7),
        priority_order INTEGER,
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_resolution (
        resolution_id NUMERIC(18) PRIMARY KEY,
        resolution_name VARCHAR(255) NOT NULL,
        description TEXT,
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_issuestatus (
        status_id NUMERIC(18) PRIMARY KEY,
        status_name VARCHAR(255) NOT NULL,
        status_category VARCHAR(50),
        description TEXT,
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    # 6. CUSTOM FIELDS & LABELS - Flexible content
    '''CREATE TABLE load.dim_customfield (
        customfield_id NUMERIC(18) PRIMARY KEY,
        customfield_name VARCHAR(255) NOT NULL,
        customfield_type VARCHAR(100) NOT NULL,
        description TEXT,
        searcher_key VARCHAR(255),
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_label (
        label_id NUMERIC(18) PRIMARY KEY,
        label_name VARCHAR(255) NOT NULL,
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_issuelinktype (
        linktype_id NUMERIC(18) PRIMARY KEY,
        linktype_name VARCHAR(255) NOT NULL,
        inward_description VARCHAR(255),
        outward_description VARCHAR(255),
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    # 7. WORKFLOW DIMENSIONS - Process management
    '''CREATE TABLE load.dim_jiraworkflows (
        workflow_id NUMERIC(18) PRIMARY KEY,
        workflow_name VARCHAR(255) NOT NULL,
        description TEXT,
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_workflowscheme (
        scheme_id NUMERIC(18) PRIMARY KEY,
        scheme_name VARCHAR(255) NOT NULL,
        description TEXT,
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_workflowschemeentity (
        entity_id NUMERIC(18) PRIMARY KEY,
        scheme_id NUMERIC(18),
        workflow_id NUMERIC(18),
        issuetype_id NUMERIC(18),
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (scheme_id) REFERENCES load.dim_workflowscheme(scheme_id),
        FOREIGN KEY (workflow_id) REFERENCES load.dim_jiraworkflows(workflow_id),
        FOREIGN KEY (issuetype_id) REFERENCES load.dim_issuetype(issuetype_id)
    );''',

    # 8. FIELD CONFIGURATION DIMENSIONS - UI management
    '''CREATE TABLE load.dim_fieldconfigscheme (
        fieldconfigscheme_id NUMERIC(18) PRIMARY KEY,
        scheme_name VARCHAR(255) NOT NULL,
        description TEXT,
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_fieldconfiguration (
        fieldconfig_id NUMERIC(18) PRIMARY KEY,
        config_name VARCHAR(255) NOT NULL,
        description TEXT,
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_fieldscreen (
        fieldscreen_id NUMERIC(18) PRIMARY KEY,
        screen_name VARCHAR(255) NOT NULL,
        description TEXT,
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_fieldscreentab (
        screentab_id NUMERIC(18) PRIMARY KEY,
        tab_name VARCHAR(255) NOT NULL,
        fieldscreen_id NUMERIC(18),
        tab_position INTEGER,
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (fieldscreen_id) REFERENCES load.dim_fieldscreen(fieldscreen_id)
    );''',

    # 9. PERMISSION & SECURITY DIMENSIONS
    '''CREATE TABLE load.dim_permissionscheme (
        permissionscheme_id NUMERIC(18) PRIMARY KEY,
        scheme_name VARCHAR(255) NOT NULL,
        description TEXT,
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_schemepermissions (
        schemepermission_id NUMERIC(18) PRIMARY KEY,
        scheme_id NUMERIC(18),
        permission_type VARCHAR(100) NOT NULL,
        permission_parameter VARCHAR(255),
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (scheme_id) REFERENCES load.dim_permissionscheme(permissionscheme_id)
    );''',

    '''CREATE TABLE load.dim_projectrole (
        projectrole_id NUMERIC(18) PRIMARY KEY,
        role_name VARCHAR(255) NOT NULL,
        description TEXT,
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_projectroleactor (
        projectroleactor_id NUMERIC(18) PRIMARY KEY,
        project_id NUMERIC(18),
        projectrole_id NUMERIC(18),
        actor_type VARCHAR(50),
        actor_parameter VARCHAR(255),
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES load.dim_project(project_id),
        FOREIGN KEY (projectrole_id) REFERENCES load.dim_projectrole(projectrole_id)
    );''',

    # 10. SYSTEM & PLUGIN DIMENSIONS
    '''CREATE TABLE load.dim_pluginversion (
        pluginversion_id NUMERIC(18) PRIMARY KEY,
        plugin_key VARCHAR(255) NOT NULL,
        plugin_name VARCHAR(255),
        version_name VARCHAR(255) NOT NULL,
        enabled BOOLEAN DEFAULT TRUE,
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_managedconfigurationitem (
        manageditem_id NUMERIC(18) PRIMARY KEY,
        item_type VARCHAR(255) NOT NULL,
        item_key VARCHAR(255),
        managed_by VARCHAR(255),
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    # 11. AGILE & AUDIT DIMENSIONS (AO tables)
    '''CREATE TABLE load.dim_ao_60db71_rapidview (
        rapidview_id NUMERIC(18) PRIMARY KEY,
        rapidview_name VARCHAR(255) NOT NULL,
        owner_user_name VARCHAR(255),
        board_type VARCHAR(50),
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_ao_c77861_audit_action_cache (
        action_cache_id NUMERIC(18) PRIMARY KEY,
        action_name VARCHAR(255) NOT NULL,
        action_category VARCHAR(100),
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    '''CREATE TABLE load.dim_ao_c77861_audit_category_cache (
        category_cache_id NUMERIC(18) PRIMARY KEY,
        category_name VARCHAR(255) NOT NULL,
        category_type VARCHAR(100),
        instance_id INTEGER NOT NULL,
        transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );''',

    # ============================================================================
    # FACT TABLES - Core business events and transactions
    # ============================================================================

    # 1. MAIN FACT TABLE - Issues (Core business entity)
    '''CREATE TABLE load.fact_jiraissue (
        issue_id NUMERIC(18) PRIMARY KEY,
        issue_key VARCHAR(50) UNIQUE NOT NULL,
        project_id NUMERIC(18) NOT NULL,
        reporter_id NUMERIC(18),
        assignee_id NUMERIC(18),
        creator_id NUMERIC(18),
        issuetype_id NUMERIC(18) NOT NULL,
        priority_id NUMERIC(18),
        status_id NUMERIC(18) NOT NULL,
        resolution_id NUMERIC(18),
        component_id NUMERIC(18),
        created_date DATE NOT NULL,
        updated_date DATE,
        resolution_date DATE,
        due_date DATE,
        time_spent NUMERIC(18),
        time_estimate NUMERIC(18),
        time_original_estimate NUMERIC(18),
        story_points NUMERIC(18,2),
        summary TEXT NOT NULL,
        description TEXT,
        environment TEXT,
        -- ETL columns
        instance_id INTEGER NOT NULL,
        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        -- Foreign Keys
        FOREIGN KEY (project_id) REFERENCES load.dim_project(project_id),
        FOREIGN KEY (reporter_id) REFERENCES load.dim_cwd_user(user_id),
        FOREIGN KEY (assignee_id) REFERENCES load.dim_cwd_user(user_id),
        FOREIGN KEY (creator_id) REFERENCES load.dim_cwd_user(user_id),
        FOREIGN KEY (issuetype_id) REFERENCES load.dim_issuetype(issuetype_id),
        FOREIGN KEY (priority_id) REFERENCES load.dim_priority(priority_id),
        FOREIGN KEY (status_id) REFERENCES load.dim_issuestatus(status_id),
        FOREIGN KEY (resolution_id) REFERENCES load.dim_resolution(resolution_id),
        FOREIGN KEY (component_id) REFERENCES load.dim_component(component_id),
        FOREIGN KEY (created_date) REFERENCES load.dim_date(date_id),
        FOREIGN KEY (updated_date) REFERENCES load.dim_date(date_id),
        FOREIGN KEY (resolution_date) REFERENCES load.dim_date(date_id),
        FOREIGN KEY (due_date) REFERENCES load.dim_date(date_id)
    );''',

    # 2. WORKLOG FACT TABLE - Time tracking events
    '''CREATE TABLE load.fact_worklog (
        worklog_id NUMERIC(18) PRIMARY KEY,
        issue_id NUMERIC(18) NOT NULL,
        author_id NUMERIC(18) NOT NULL,
        worklog_date DATE NOT NULL,
        time_spent NUMERIC(18) NOT NULL,
        time_spent_seconds NUMERIC(18),
        work_description TEXT,
        work_started TIMESTAMP,
        -- ETL columns
        instance_id INTEGER NOT NULL,
        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        -- Foreign Keys
        FOREIGN KEY (issue_id) REFERENCES load.fact_jiraissue(issue_id),
        FOREIGN KEY (author_id) REFERENCES load.dim_cwd_user(user_id),
        FOREIGN KEY (worklog_date) REFERENCES load.dim_date(date_id)
    );''',

    # 3. CUSTOM FIELD VALUES FACT TABLE - Flexible data storage
    '''CREATE TABLE load.fact_customfieldvalue (
        customfieldvalue_id NUMERIC(18) PRIMARY KEY,
        issue_id NUMERIC(18) NOT NULL,
        customfield_id NUMERIC(18) NOT NULL,
        string_value VARCHAR(255),
        number_value NUMERIC(18,2),
        text_value TEXT,
        date_value DATE,
        -- ETL columns
        instance_id INTEGER NOT NULL,
        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        -- Foreign Keys
        FOREIGN KEY (issue_id) REFERENCES load.fact_jiraissue(issue_id),
        FOREIGN KEY (customfield_id) REFERENCES load.dim_customfield(customfield_id),
        FOREIGN KEY (date_value) REFERENCES load.dim_date(date_id)
    );''',

    # 4. FILE ATTACHMENT FACT TABLE - Document management
    '''CREATE TABLE load.fact_fileattachment (
        attachment_id NUMERIC(18) PRIMARY KEY,
        issue_id NUMERIC(18) NOT NULL,
        author_id NUMERIC(18),
        filename VARCHAR(255) NOT NULL,
        file_size NUMERIC(18),
        mimetype VARCHAR(100),
        created_date DATE,
        -- ETL columns
        instance_id INTEGER NOT NULL,
        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        -- Foreign Keys
        FOREIGN KEY (issue_id) REFERENCES load.fact_jiraissue(issue_id),
        FOREIGN KEY (author_id) REFERENCES load.dim_cwd_user(user_id),
        FOREIGN KEY (created_date) REFERENCES load.dim_date(date_id)
    );''',

    # 5. AUDIT ENTITY FACT TABLE - Change tracking and audit trail
    '''CREATE TABLE load.fact_ao_c77861_audit_entity (
        audit_id NUMERIC(18) PRIMARY KEY,
        user_id NUMERIC(18),
        action_id NUMERIC(18),
        category_id NUMERIC(18),
        object_type VARCHAR(255),
        object_id NUMERIC(18),
        object_name VARCHAR(255),
        audit_timestamp TIMESTAMP,
        audit_date DATE,
        primary_resource_id NUMERIC(18),
        secondary_resource_id NUMERIC(18),
        -- ETL columns
        instance_id INTEGER NOT NULL,
        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        -- Foreign Keys
        FOREIGN KEY (user_id) REFERENCES load.dim_cwd_user(user_id),
        FOREIGN KEY (action_id) REFERENCES load.dim_ao_c77861_audit_action_cache(action_cache_id),
        FOREIGN KEY (category_id) REFERENCES load.dim_ao_c77861_audit_category_cache(category_cache_id),
        FOREIGN KEY (audit_date) REFERENCES load.dim_date(date_id)
    );''',

    # 6. AGILE BOARD FACT TABLE - Sprint and board activities
    '''CREATE TABLE load.fact_sprint_issue (
        sprint_issue_id NUMERIC(18) PRIMARY KEY,
        rapidview_id NUMERIC(18) NOT NULL,
        issue_id NUMERIC(18) NOT NULL,
        sprint_id NUMERIC(18),
        added_date DATE,
        removed_date DATE,
        issue_rank NUMERIC(18),
        -- ETL columns
        instance_id INTEGER NOT NULL,
        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        -- Foreign Keys
        FOREIGN KEY (rapidview_id) REFERENCES load.dim_ao_60db71_rapidview(rapidview_id),
        FOREIGN KEY (issue_id) REFERENCES load.fact_jiraissue(issue_id),
        FOREIGN KEY (added_date) REFERENCES load.dim_date(date_id),
        FOREIGN KEY (removed_date) REFERENCES load.dim_date(date_id)
    );''',

    # ============================================================================
    # PERFORMANCE INDEXES - Optimized for constellation schema queries
    # ============================================================================

    # Core fact table indexes
    '''CREATE INDEX idx_fact_jiraissue_project ON load.fact_jiraissue(project_id);''',
    '''CREATE INDEX idx_fact_jiraissue_assignee ON load.fact_jiraissue(assignee_id);''',
    '''CREATE INDEX idx_fact_jiraissue_status ON load.fact_jiraissue(status_id);''',
    '''CREATE INDEX idx_fact_jiraissue_created_date ON load.fact_jiraissue(created_date);''',
    '''CREATE INDEX idx_fact_jiraissue_issuetype ON load.fact_jiraissue(issuetype_id);''',

    # Worklog fact indexes
    '''CREATE INDEX idx_fact_worklog_issue ON load.fact_worklog(issue_id);''',
    '''CREATE INDEX idx_fact_worklog_author ON load.fact_worklog(author_id);''',
    '''CREATE INDEX idx_fact_worklog_date ON load.fact_worklog(worklog_date);''',

    # Custom field fact indexes
    '''CREATE INDEX idx_fact_customfieldvalue_issue ON load.fact_customfieldvalue(issue_id);''',
    '''CREATE INDEX idx_fact_customfieldvalue_field ON load.fact_customfieldvalue(customfield_id);''',

    # Audit fact indexes
    '''CREATE INDEX idx_fact_audit_user ON load.fact_ao_c77861_audit_entity(user_id);''',
    '''CREATE INDEX idx_fact_audit_date ON load.fact_ao_c77861_audit_entity(audit_date);''',
    '''CREATE INDEX idx_fact_audit_object ON load.fact_ao_c77861_audit_entity(object_type, object_id);''',

    # Sprint fact indexes
    '''CREATE INDEX idx_fact_sprint_issue_board ON load.fact_sprint_issue(rapidview_id);''',
    '''CREATE INDEX idx_fact_sprint_issue_issue ON load.fact_sprint_issue(issue_id);'''
]

class LoadDDLBuilder:
    def __init__(self):
        try:
            parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            config_file = os.path.join(parent_dir, 'etl_config.json')
            self.config_manager = ETLConfigManager(config_file)
            db_config = self.config_manager.config["database"]
            self.dw_config = {
                'host': db_config['host'],
                'port': db_config['port'],
                'database': db_config['target_db'],
                'user': db_config['user'],
                'password': db_config['password']
            }
        except Exception as e:
            logger.error(f"Config load failed: {e}")
            raise

    def connect_dw(self):
        try:
            return psycopg2.connect(**self.dw_config)
        except Exception as e:
            logger.error(f"DW connection error: {e}")
            raise

    def run_ddl(self):
        conn = self.connect_dw()
        try:
            with conn.cursor() as cursor:
                for stmt in DDL_STATEMENTS:
                    try:
                        cursor.execute(stmt)
                    except Exception as e:
                        logger.error(f"Error executing: {stmt[:100]}... Error: {e}")
                        conn.rollback()
                        raise
                conn.commit()
                logger.info("All DDL statements executed successfully")
        finally:
            conn.close()

if __name__ == "__main__":
    logger.info("Starting Load DDL Execution")
    try:
        builder = LoadDDLBuilder()
        builder.run_ddl()
        logger.info("DDL completed successfully")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)