 DROP SCHEMA "transform";



CREATE SCHEMA "transform" AUTHORIZATION jirauser;

-- "transform"."AO_60DB71_RAPIDVIEW_clean" definition



-- Drop table



-- DROP TABLE "transform"."AO_60DB71_RAPIDVIEW_clean";



CREATE TABLE "transform"."AO_60DB71_RAPIDVIEW_clean" (

"CARD_COLOR_STRATEGY" varchar(255) NULL,

"ID" numeric(18) NOT NULL,

"KAN_PLAN_ENABLED" bool NULL,

"NAME" varchar(255) NOT NULL,

"OLD_DONE_ISSUES_CUTOFF" varchar(255) NULL,

"OWNER_USER_NAME" varchar(255) NOT NULL,

"REFINED_VELOCITY_ACTIVE" bool NULL,

"SAVED_FILTER_ID" int8 NOT NULL,

"SHOW_DAYS_IN_COLUMN" bool NULL,

"SHOW_EPIC_AS_PANEL" bool NULL,

"SPRINTS_ENABLED" bool NULL,

"SWIMLANE_STRATEGY" varchar(255) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform"."AO_60DB71_RAPIDVIEW_clean" OWNER TO jirauser;

GRANT ALL ON TABLE "transform"."AO_60DB71_RAPIDVIEW_clean" TO jirauser;





-- "transform"."AO_C77861_AUDIT_ACTION_CACHE_clean" definition



-- Drop table



-- DROP TABLE "transform"."AO_C77861_AUDIT_ACTION_CACHE_clean";



CREATE TABLE "transform"."AO_C77861_AUDIT_ACTION_CACHE_clean" (

"ACTION" varchar(255) NOT NULL,

"ACTION_T_KEY" varchar(255) NULL,

"ID" numeric(18) NOT NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform"."AO_C77861_AUDIT_ACTION_CACHE_clean" OWNER TO jirauser;

GRANT ALL ON TABLE "transform"."AO_C77861_AUDIT_ACTION_CACHE_clean" TO jirauser;





-- "transform"."AO_C77861_AUDIT_CATEGORY_CACHE_clean" definition



-- Drop table



-- DROP TABLE "transform"."AO_C77861_AUDIT_CATEGORY_CACHE_clean";



CREATE TABLE "transform"."AO_C77861_AUDIT_CATEGORY_CACHE_clean" (

"CATEGORY" varchar(255) NOT NULL,

"CATEGORY_T_KEY" varchar(255) NULL,

"ID" numeric(18) NOT NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform"."AO_C77861_AUDIT_CATEGORY_CACHE_clean" OWNER TO jirauser;

GRANT ALL ON TABLE "transform"."AO_C77861_AUDIT_CATEGORY_CACHE_clean" TO jirauser;





-- "transform"."AO_C77861_AUDIT_ENTITY_clean" definition



-- Drop table



-- DROP TABLE "transform"."AO_C77861_AUDIT_ENTITY_clean";



CREATE TABLE "transform"."AO_C77861_AUDIT_ENTITY_clean" (

"ACTION" varchar(255) NOT NULL,

"ACTION_T_KEY" varchar(255) NULL,

"AREA" varchar(255) NOT NULL,

"ATTRIBUTES" text NULL,

"CATEGORY" varchar(255) NULL,

"CATEGORY_T_KEY" varchar(255) NULL,

"CHANGE_VALUES" text NULL,

"ENTITY_TIMESTAMP" int8 NOT NULL,

"ID" numeric(18) NOT NULL,

"LEVEL" varchar(255) NOT NULL,

"METHOD" varchar(255) NULL,

"PRIMARY_RESOURCE_ID" varchar(255) NULL,

"PRIMARY_RESOURCE_TYPE" varchar(255) NULL,

"RESOURCES" text NULL,

"SEARCH_STRING" text NULL,

"SECONDARY_RESOURCE_ID" varchar(255) NULL,

"SECONDARY_RESOURCE_TYPE" varchar(255) NULL,

"SOURCE" varchar(255) NULL,

"SYSTEM_INFO" varchar(255) NULL,

"USER_ID" varchar(255) NULL,

"USER_NAME" varchar(255) NULL,

"USER_TYPE" varchar(255) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform"."AO_C77861_AUDIT_ENTITY_clean" OWNER TO jirauser;

GRANT ALL ON TABLE "transform"."AO_C77861_AUDIT_ENTITY_clean" TO jirauser;





-- "transform".component_clean definition



-- Drop table



-- DROP TABLE "transform".component_clean;



CREATE TABLE "transform".component_clean (

id numeric(18) NOT NULL,

project numeric(18) NULL,

cname varchar(255) NULL,

archived varchar(10) NULL,

deleted varchar(10) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".component_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".component_clean TO jirauser;





-- "transform".customfield_clean definition



-- Drop table



-- DROP TABLE "transform".customfield_clean;



CREATE TABLE "transform".customfield_clean (

id numeric(18) NOT NULL,

customfieldtypekey varchar(255) NULL,

customfieldsearcherkey varchar(255) NULL,

cfname varchar(255) NULL,

project numeric(18) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".customfield_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".customfield_clean TO jirauser;





-- "transform".customfieldvalue_clean definition



-- Drop table



-- DROP TABLE "transform".customfieldvalue_clean;



CREATE TABLE "transform".customfieldvalue_clean (

id numeric(18) NOT NULL,

issue numeric(18) NULL,

customfield numeric(18) NULL,

stringvalue varchar(255) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".customfieldvalue_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".customfieldvalue_clean TO jirauser;





-- "transform".cwd_group_clean definition



-- Drop table



-- DROP TABLE "transform".cwd_group_clean;



CREATE TABLE "transform".cwd_group_clean (

id numeric(18) NOT NULL,

group_name varchar(255) NULL,

lower_group_name varchar(255) NULL,

active numeric(9) NULL,

created_date timestamptz NULL,

updated_date timestamptz NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".cwd_group_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".cwd_group_clean TO jirauser;





-- "transform".cwd_membership_clean definition



-- Drop table



-- DROP TABLE "transform".cwd_membership_clean;



CREATE TABLE "transform".cwd_membership_clean (

id numeric(18) NOT NULL,

parent_id numeric(18) NULL,

child_id numeric(18) NULL,

membership_type varchar(60) NULL,

parent_name varchar(255) NULL,

lower_parent_name varchar(255) NULL,

child_name varchar(255) NULL,

lower_child_name varchar(255) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".cwd_membership_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".cwd_membership_clean TO jirauser;





-- "transform".cwd_user_clean definition



-- Drop table



-- DROP TABLE "transform".cwd_user_clean;



CREATE TABLE "transform".cwd_user_clean (

id numeric(18) NOT NULL,

user_name varchar(255) NULL,

active numeric(9) NULL,

created_date timestamptz NULL,

updated_date timestamptz NULL,

email_address varchar(255) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".cwd_user_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".cwd_user_clean TO jirauser;





-- "transform".fieldconfigscheme_clean definition



-- Drop table



-- DROP TABLE "transform".fieldconfigscheme_clean;



CREATE TABLE "transform".fieldconfigscheme_clean (

id numeric(18) NOT NULL,

configname varchar(255) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".fieldconfigscheme_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".fieldconfigscheme_clean TO jirauser;





-- "transform".fieldconfiguration_clean definition



-- Drop table



-- DROP TABLE "transform".fieldconfiguration_clean;



CREATE TABLE "transform".fieldconfiguration_clean (

id numeric(18) NOT NULL,

configname varchar(255) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".fieldconfiguration_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".fieldconfiguration_clean TO jirauser;





-- "transform".fieldscreen_clean definition



-- Drop table



-- DROP TABLE "transform".fieldscreen_clean;



CREATE TABLE "transform".fieldscreen_clean (

id numeric(18) NOT NULL,

"name" varchar(255) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".fieldscreen_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".fieldscreen_clean TO jirauser;





-- "transform".fieldscreentab_clean definition



-- Drop table



-- DROP TABLE "transform".fieldscreentab_clean;



CREATE TABLE "transform".fieldscreentab_clean (

id numeric(18) NOT NULL,

"name" varchar(255) NULL,

fieldscreen numeric(18) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".fieldscreentab_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".fieldscreentab_clean TO jirauser;





-- "transform".fileattachment_clean definition



-- Drop table



-- DROP TABLE "transform".fileattachment_clean;



CREATE TABLE "transform".fileattachment_clean (

id numeric(18) NOT NULL,

issueid numeric(18) NULL,

mimetype varchar(255) NULL,

filename varchar(255) NULL,

created timestamptz NULL,

filesize numeric(18) NULL,

author numeric(18) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".fileattachment_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".fileattachment_clean TO jirauser;





-- "transform".issuelinktype_clean definition



-- Drop table



-- DROP TABLE "transform".issuelinktype_clean;



CREATE TABLE "transform".issuelinktype_clean (

id numeric(18) NOT NULL,

linkname varchar(255) NULL,

inward varchar(255) NULL,

outward varchar(255) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".issuelinktype_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".issuelinktype_clean TO jirauser;





-- "transform".issuestatus_clean definition



-- Drop table



-- DROP TABLE "transform".issuestatus_clean;



CREATE TABLE "transform".issuestatus_clean (

id numeric(18) NOT NULL,

pname varchar(60) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".issuestatus_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".issuestatus_clean TO jirauser;





-- "transform".issuetype_clean definition



-- Drop table



-- DROP TABLE "transform".issuetype_clean;



CREATE TABLE "transform".issuetype_clean (

id numeric(18) NOT NULL,

pname varchar(60) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".issuetype_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".issuetype_clean TO jirauser;





-- "transform".jiraissue_clean definition



-- Drop table



-- DROP TABLE "transform".jiraissue_clean;



CREATE TABLE "transform".jiraissue_clean (

id numeric(18) NOT NULL,

issuenum numeric(18) NULL,

project numeric(18) NULL,

reporter numeric(18) NULL,

assignee numeric(18) NULL,

creator numeric(18) NULL,

summary varchar(255) NULL,

priority numeric(18) NULL,

resolution numeric(18) NULL,

issuestatus numeric(18) NULL,

created timestamptz NULL,

duedate timestamptz NULL,

resolutiondate timestamptz NULL,

timeoriginalestimate numeric(18) NULL,

timeestimate numeric(18) NULL,

timespent numeric(18) NULL,

workflow_id numeric(18) NULL,

archived bpchar(1) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".jiraissue_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".jiraissue_clean TO jirauser;





-- "transform".jiraworkflows_clean definition



-- Drop table



-- DROP TABLE "transform".jiraworkflows_clean;



CREATE TABLE "transform".jiraworkflows_clean (

id numeric(18) NOT NULL,

workflowname varchar(255) NULL,

"descriptor" text NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".jiraworkflows_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".jiraworkflows_clean TO jirauser;





-- "transform".label_clean definition



-- Drop table



-- DROP TABLE "transform".label_clean;



CREATE TABLE "transform".label_clean (

id numeric(18) NOT NULL,

issue numeric(18) NULL,

"label" varchar(255) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".label_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".label_clean TO jirauser;





-- "transform".managedconfigurationitem_clean definition



-- Drop table



-- DROP TABLE "transform".managedconfigurationitem_clean;



CREATE TABLE "transform".managedconfigurationitem_clean (

id numeric(18) NOT NULL,

item_id numeric(18) NULL,

item_type varchar(255) NULL,

managed varchar(10) NULL,

access_level varchar(255) NULL,

"source" varchar(255) NULL,

description_key varchar(255) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".managedconfigurationitem_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".managedconfigurationitem_clean TO jirauser;





-- "transform".permissionscheme_clean definition



-- Drop table



-- DROP TABLE "transform".permissionscheme_clean;



CREATE TABLE "transform".permissionscheme_clean (

id numeric(18) NOT NULL,

"name" varchar(255) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".permissionscheme_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".permissionscheme_clean TO jirauser;





-- "transform".pluginversion_clean definition



-- Drop table



-- DROP TABLE "transform".pluginversion_clean;



CREATE TABLE "transform".pluginversion_clean (

id numeric(18) NOT NULL,

pluginname varchar(255) NULL,

pluginkey varchar(255) NULL,

pluginversion varchar(255) NULL,

created timestamptz NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".pluginversion_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".pluginversion_clean TO jirauser;





-- "transform".priority_clean definition



-- Drop table



-- DROP TABLE "transform".priority_clean;



CREATE TABLE "transform".priority_clean (

id numeric(18) NOT NULL,

pname varchar(60) NULL,

status_color varchar(60) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".priority_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".priority_clean TO jirauser;





-- "transform".project_clean definition



-- Drop table



-- DROP TABLE "transform".project_clean;



CREATE TABLE "transform".project_clean (

id numeric(18) NOT NULL,

pname varchar(255) NULL,

"lead" numeric(18) NULL,

pcounter numeric(18) NULL,

assigneetype numeric(18) NULL,

projecttype varchar(255) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".project_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".project_clean TO jirauser;





-- "transform".projectrole_clean definition



-- Drop table



-- DROP TABLE "transform".projectrole_clean;



CREATE TABLE "transform".projectrole_clean (

id numeric(18) NOT NULL,

"name" varchar(255) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);



-- Permissions



ALTER TABLE "transform".projectrole_clean OWNER TO jirauser;

GRANT ALL ON TABLE "transform".projectrole_clean TO jirauser;





-- "transform".projectroleactor_clean definition



-- Drop table



-- DROP TABLE "transform".projectroleactor_clean;



CREATE TABLE "transform".projectroleactor_clean (

id numeric(18) NOT NULL,

pid numeric(18) NULL,

projectroleid numeric(18) NULL,

roletype varchar(255) NULL,

roletypeparameter varchar(255) NULL,

instance_id int4 NOT NULL,

transformed_at timestamp DEFAULT CURRENT_TIMESTAMP NULL

);

<