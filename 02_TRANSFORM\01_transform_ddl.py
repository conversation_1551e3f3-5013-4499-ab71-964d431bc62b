#!/usr/bin/env python3
"""
TRANSFORM DDL - CREATE CLEAN TABLES
Creates 33 transform tables with transformations from transform_changes.txt:
- Drop NULL/useless columns (e.g., iconurl, sequence, archiveddate)
- Convert user columns (e.g., reporter, assignee, lead) to NUMERIC(18)
- Extract numeric values from customfield_ columns (e.g., fieldid, item_id)
- Ensure data type consistency with staging
- Add ETL tracking columns (instance_id, transformed_at)
"""

import psycopg2
import logging
import re
from datetime import datetime
import sys
import os

# Add utilities directory to path for imports
utilities_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '05_UTILITIES')
sys.path.append(utilities_path)
from config_manager import ETLConfigManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('transform_ddl.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class TransformDDLBuilder:
    def __init__(self):
        """Initialize the transform schema builder"""
        # Load configuration
        try:
            parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            config_file = os.path.join(parent_dir, 'etl_config.json')
            self.config_manager = ETLConfigManager(config_file)
            db_config = self.config_manager.config["database"]
            self.dw_config = {
                'host': db_config['host'],
                'port': db_config['port'],
                'database': db_config['target_db'],
                'user': db_config['user'],
                'password': db_config['password']
            }
        except Exception as e:
            logger.warning(f"Config manager failed: {e}, using fallback config")
            self.dw_config = {
                'host': 'localhost',
                'port': 5432,
                'database': 'test',
                'user': 'jirauser',
                'password': 'mypassword'
            }

        self.stats = {
            'start_time': datetime.now(),
            'tables_created': 0,
            'total_columns': 0,
            'null_columns_dropped': 0,
            'errors': []
        }

        # 33 critical tables
        self.all_critical_tables = [
            'jiraissue', 'project', 'component', 'customfield', 'customfieldvalue',
            'worklog', 'fileattachment', 'issuelinktype', 'label', 'cwd_group',
            'cwd_user', 'cwd_membership', 'projectroleactor', 'jiraworkflows',
            'workflowscheme', 'workflowschemeentity', 'fieldconfigscheme',
            'fieldconfiguration', 'fieldscreen', 'fieldscreentab', 'permissionscheme',
            'schemepermissions', 'priority', 'issuestatus', 'resolution', 'issuetype',
            'projectrole', 'pluginversion', 'managedconfigurationitem',
            'AO_60DB71_RAPIDVIEW', 'AO_C77861_AUDIT_ENTITY',
            'AO_C77861_AUDIT_ACTION_CACHE', 'AO_C77861_AUDIT_CATEGORY_CACHE'
        ]

        # Common NULL columns to drop
        self.null_columns_to_drop = [
            'iconurl', 'sequence', 'url', 'avatar', 'originalkey', 'fieldid',
            'creatorname', 'islocked', 'pkey', 'environment', 'security', 'fixfor',
            'component', 'archivedby', 'archiveddate', 'zip', 'thumbnailable',
            'description', 'pstyle', 'avatarurl', 'statuscategory', 'directory_id',
            'first_name', 'lower_first_name', 'last_name', 'lower_last_name',
            'display_name', 'lower_display_name', 'lower_email_address', 'credential',
            'deleted_externally', 'external_id', 'group_type', 'local', 'lower_description',
            'cfkey', 'defaultvalue', 'fieldtype', 'issuetype', 'issueswithvalue',
            'lastvalueupdate', 'updated', 'parentkey', 'numbervalue', 'textvalue',
            'datevalue', 'valuetype', 'node', 'resource_id_3', 'resource_id_4',
            'resource_id_5', 'resource_type_3', 'resource_type_4', 'resource_type_5'
        ]

        # Table-specific columns to drop
        self.table_specific_drops = {
            'jiraissue': ['watches', 'votes'],
            'project': ['avatar'],
            'cwd_user': ['lower_user_name', 'upper_user_name', 'upper_email_address', 'lower_email_address', 'upper_display_name', 'lower_display_name'],
            'fileattachment': ['thumbnail'],
            'component': ['description', 'lead', 'assigneetype'],
            'fieldconfiguration': ['customfield'],
            'fieldconfigscheme': ['customfield'],
            'fieldscreentab': ['description', 'sequence'],
            'cwd_group': ['description'],
            'cwd_membership': ['group_type']
        }

        # Columns requiring numeric extraction
        self.user_columns = ['reporter', 'assignee', 'creator', 'lead', 'author', 'owner_user_name']
        self.customfield_columns = ['item_id', 'fieldid']

    def connect_dw(self):
        """Connect to the data warehouse"""
        try:
            return psycopg2.connect(**self.dw_config)
        except Exception as e:
            logger.error(f"DW connection error: {e}")
            raise

    def get_staging_table_structure(self, table_name):
        """Retrieve exact structure of a staging table"""
        conn = self.connect_dw()
        cursor = conn.cursor()
        try:
            cursor.execute("""
                SELECT 
                    column_name, data_type, character_maximum_length, 
                    numeric_precision, numeric_scale, is_nullable, ordinal_position
                FROM information_schema.columns
                WHERE table_schema = 'staging' AND table_name = %s
                AND column_name NOT IN ('instance_id', 'extracted_at')
                ORDER BY ordinal_position
            """, (table_name,))
            columns = cursor.fetchall()
            if not columns:
                logger.warning(f"Table staging.{table_name} not found")
                return None
            return columns
        except Exception as e:
            logger.error(f"Error retrieving structure for {table_name}: {e}")
            return None
        finally:
            conn.close()

    def should_drop_column(self, column_name, table_name):
        """Determine if a column should be dropped"""
        if column_name.lower() in [col.lower() for col in self.null_columns_to_drop]:
            return True
        if table_name in self.table_specific_drops and column_name.lower() in [col.lower() for col in self.table_specific_drops[table_name]]:
            return True
        return False

    def should_convert_to_numeric(self, column_name, table_name):
        """Check if column should be converted to NUMERIC(18)"""
        if any(user_col.lower() in column_name.lower() for user_col in self.user_columns):
            return True
        if any(cf_col.lower() in column_name.lower() for cf_col in self.customfield_columns):
            return True
        if column_name.lower() in ['id', 'project', 'issueid', 'pid', 'projectroleid', 'scheme', 'permission', 'pcounter', 
                                  'assigneetype', 'filesize', 'workflow_id', 'issuenum', 'timeoriginalestimate', 
                                  'timeestimate', 'timespent', 'customfield', 'issue', 'fieldscreen', 'parent_id', 
                                  'child_id', 'directory_id', 'issuestatus', 'resolution', 'priority', 'issuetype']:
            return True
        if table_name == 'jiraissue' and column_name.lower() in ['issuestatus', 'resolution', 'priority', 'issuetype']:
            return True
        if table_name in ['issuestatus', 'issuetype'] and column_name.lower() == 'id':
            return True
        if table_name == 'pluginversion' and column_name.lower() == 'pluginversion':
            return True
        return False

    def detect_optimal_data_type(self, table_name, column_name, current_type, sample_size=100):
        """Dynamically detect optimal data type based on staging data"""
        conn = self.connect_dw()
        cursor = conn.cursor()
        try:
            cursor.execute(f"""
                SELECT "{column_name}"
                FROM staging."{table_name}"
                WHERE "{column_name}" IS NOT NULL
                LIMIT %s
            """, (sample_size,))
            sample_data = [row[0] for row in cursor.fetchall()]
            if not sample_data:
                return current_type
            all_numeric = True
            all_integer = True
            max_length = 0
            for value in sample_data:
                str_value = str(value).strip()
                max_length = max(max_length, len(str_value))
                try:
                    float_val = float(str_value)
                    if '.' in str_value or 'e' in str_value.lower():
                        all_integer = False
                except (ValueError, TypeError):
                    all_numeric = False
                    all_integer = False
                    break
            if all_numeric and all_integer:
                return 'NUMERIC(18)'
            elif all_numeric:
                return 'NUMERIC(18,2)'
            elif max_length <= 50:
                return f'VARCHAR({max_length + 20})'
            elif max_length <= 255:
                return f'VARCHAR({max_length + 50})'
            else:
                return 'TEXT'
        except Exception as e:
            logger.debug(f"Dynamic type detection failed for {column_name}: {e}")
            return current_type
        finally:
            conn.close()

    def build_column_definition(self, column_info, table_name):
        """Build PostgreSQL column definition"""
        col_name, data_type, char_length, num_precision, num_scale, is_nullable, _ = column_info
        if self.should_convert_to_numeric(col_name, table_name):
            # Special case: pluginversion column in pluginversion table and OWNER_USER_NAME in AO_60DB71_RAPIDVIEW should remain VARCHAR(255)
            if (table_name == 'pluginversion' and col_name == 'pluginversion') or (table_name == 'AO_60DB71_RAPIDVIEW' and col_name == 'OWNER_USER_NAME'):
                pg_type = 'VARCHAR(255)'
            else:
                pg_type = 'NUMERIC(18)'
            logger.debug(f"Converting {col_name} to {pg_type}")
        else:
            if data_type == 'character varying':
                pg_type = f'VARCHAR({char_length})' if char_length else 'TEXT'
            elif data_type == 'character':
                pg_type = f'CHAR({char_length})' if char_length else 'CHAR(1)'
            elif data_type == 'text':
                pg_type = 'TEXT'
            elif data_type == 'integer':
                pg_type = 'INTEGER'
            elif data_type == 'bigint':
                pg_type = 'BIGINT'
            elif data_type == 'smallint':
                pg_type = 'SMALLINT'
            elif data_type == 'numeric':
                if num_precision and num_scale:
                    pg_type = f'NUMERIC({num_precision},{num_scale})'
                elif num_precision:
                    pg_type = f'NUMERIC({num_precision})'
                else:
                    pg_type = 'NUMERIC'
            elif data_type == 'timestamp without time zone':
                pg_type = 'TIMESTAMP'
            elif data_type == 'timestamp with time zone':
                pg_type = 'TIMESTAMPTZ'
            elif data_type == 'date':
                pg_type = 'DATE'
            elif data_type == 'boolean':
                pg_type = 'BOOLEAN'
            elif data_type == 'double precision':
                pg_type = 'DOUBLE PRECISION'
            elif data_type == 'real':
                pg_type = 'REAL'
            else:
                pg_type = self.detect_optimal_data_type(table_name, col_name, 'TEXT')
        null_constraint = '' if is_nullable == 'YES' else ' NOT NULL'
        return f'    "{col_name}" {pg_type}{null_constraint}'

    def create_transform_schema(self):
        """Create the transform schema"""
        logger.info("Creating TRANSFORM schema")
        conn = self.connect_dw()
        cursor = conn.cursor()
        try:
            cursor.execute('CREATE SCHEMA IF NOT EXISTS transform')
            conn.commit()
            logger.info("Transform schema created successfully")
        except Exception as e:
            logger.error(f"Error creating transform schema: {e}")
            conn.rollback()
            raise
        finally:
            conn.close()

    def create_transform_table(self, table_name, staging_columns):
        """Create a transform table with transformations"""
        conn = self.connect_dw()
        cursor = conn.cursor()
        try:
            transform_table_name = f"{table_name}_clean"
            filtered_columns = []
            for column_info in staging_columns:
                col_name = column_info[0]
                if self.should_drop_column(col_name, table_name):
                    self.stats['null_columns_dropped'] += 1
                    logger.debug(f"Dropping NULL column: {col_name}")
                    continue
                filtered_columns.append(self.build_column_definition(column_info, table_name))
            etl_columns = [
                '    instance_id INTEGER NOT NULL',
                '    transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
            ]
            all_columns = filtered_columns + etl_columns
            columns_ddl = ',\n'.join(all_columns)
            create_sql = f'''
                CREATE TABLE transform."{transform_table_name}" (
                {columns_ddl}
                )
            '''
            cursor.execute(f'DROP TABLE IF EXISTS transform."{transform_table_name}" CASCADE')
            cursor.execute(create_sql)
            conn.commit()
            self.stats['tables_created'] += 1
            self.stats['total_columns'] += len(all_columns)
            logger.info(f"transform.{transform_table_name}: {len(filtered_columns)} columns + 2 ETL")
        except Exception as e:
            logger.error(f"Error creating table {table_name}: {e}")
            self.stats['errors'].append(f"{table_name}: {e}")
            conn.rollback()
        finally:
            conn.close()

    def build_all_transform_tables(self):
        """Build all transform tables"""
        logger.info("\nBUILDING 33 TRANSFORM TABLES")
        for i, table_name in enumerate(self.all_critical_tables, 1):
            logger.info(f"[{i:2d}/33] Creating transform.{table_name}_clean")
            staging_columns = self.get_staging_table_structure(table_name)
            if staging_columns:
                self.create_transform_table(table_name, staging_columns)
            else:
                logger.warning(f"Table staging.{table_name} not found - skipped")
                self.stats['errors'].append(f"{table_name}: Staging table not found")

    def verify_transform_schema(self):
        """Verify transform schema creation"""
        logger.info("\nVERIFYING TRANSFORM SCHEMA")
        conn = self.connect_dw()
        cursor = conn.cursor()
        try:
            cursor.execute("""
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_schema = 'transform'
            """)
            total_tables = cursor.fetchone()[0]
            cursor.execute("""
                SELECT COUNT(*)
                FROM information_schema.columns
                WHERE table_schema = 'transform'
            """)
            total_columns = cursor.fetchone()[0]
            logger.info(f"Tables created: {total_tables}")
            logger.info(f"Total columns: {total_columns}")
            return total_tables
        except Exception as e:
            logger.error(f"Verification error: {e}")
            return 0
        finally:
            conn.close()

if __name__ == "__main__":
    print("TRANSFORM DDL - CREATE CLEAN TABLES")
    print("=" * 70)
    print("Objective: Create 33 transform tables with transformations")
    print("=" * 70)
    try:
        builder = TransformDDLBuilder()
        builder.create_transform_schema()
        builder.build_all_transform_tables()
        total_tables = builder.verify_transform_schema()
        duration = (datetime.now() - builder.stats['start_time']).total_seconds()
        logger.info(f"\nTRANSFORM SCHEMA CREATED!")
        logger.info(f"Duration: {duration:.2f} seconds")
        logger.info(f"Tables created: {builder.stats['tables_created']}")
        logger.info(f"Total columns: {builder.stats['total_columns']}")
        logger.info(f"NULL columns dropped: {builder.stats['null_columns_dropped']}")
        logger.info(f"Errors: {len(builder.stats['errors'])}")
        if builder.stats['errors']:
            logger.warning("Errors encountered:")
            for error in builder.stats['errors'][:5]:
                logger.warning(f"  - {error}")
        if total_tables == 33:
            logger.info("SUCCESS: All 33 transform tables created!")
        else:
            logger.warning(f"WARNING: {total_tables}/33 tables created")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)