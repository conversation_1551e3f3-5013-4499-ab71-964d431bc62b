{"database": {"host": "localhost", "port": 5432, "user": "<PERSON><PERSON><PERSON>", "password": "mypassword", "target_db": "lm"}, "jira_instance": {"instance_id": 1, "host": "localhost", "port": 5432, "database": "<PERSON><PERSON><PERSON>", "user": "<PERSON><PERSON><PERSON>", "password": "mypassword", "client_name": "Kraft"}, "etl_settings": {"batch_size": 20000, "timeout_seconds": 120, "max_retries": 2, "validate_data": true, "create_indexes": false, "fast_mode": true, "skip_validation": false, "parallel_processing": true}, "performance": {"connection_pool_size": 5, "commit_frequency": 10000, "disable_foreign_keys": true, "disable_triggers": true, "use_copy_instead_of_insert": true}, "schemas": {"staging": "staging", "transform": "transform", "load": "load"}}