#!/usr/bin/env python3
"""
🚀 ETL FAST MODE OPTIMIZER
Optimizes database settings for faster ETL execution

FEATURES:
- Disable foreign key constraints during ETL
- Disable triggers for faster inserts
- Optimize PostgreSQL settings for bulk operations
- Enable parallel processing where possible
"""

import psycopg2
import logging
import os
import json

logger = logging.getLogger(__name__)

class FastModeOptimizer:
    """Optimize database for fast ETL execution"""
    
    def __init__(self, config_file="etl_config.json"):
        self.config_file = config_file
        self.config = self.load_config()
        
    def load_config(self):
        """Load ETL configuration"""
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r') as f:
                return json.load(f)
        return {}
    
    def get_connection(self):
        """Get database connection"""
        db_config = self.config.get("database", {})
        return psycopg2.connect(
            host=db_config.get('host', 'localhost'),
            port=db_config.get('port', 5432),
            database=db_config.get('target_db', 'test'),
            user=db_config.get('user', 'jirauser'),
            password=db_config.get('password', 'mypassword')
        )
    
    def enable_fast_mode(self):
        """Enable fast mode optimizations"""
        logger.info("🚀 ENABLING FAST MODE OPTIMIZATIONS")
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Disable foreign key checks (PostgreSQL doesn't have global FK disable)
            # Instead, we'll use deferred constraints
            cursor.execute("SET session_replication_role = replica;")
            
            # Optimize for bulk operations
            cursor.execute("SET synchronous_commit = off;")
            cursor.execute("SET wal_buffers = '16MB';")
            cursor.execute("SET checkpoint_segments = 32;")
            cursor.execute("SET checkpoint_completion_target = 0.9;")
            cursor.execute("SET work_mem = '256MB';")
            cursor.execute("SET maintenance_work_mem = '512MB';")
            
            # Disable autovacuum during ETL
            cursor.execute("SET autovacuum = off;")
            
            conn.commit()
            logger.info("✅ Fast mode optimizations enabled")
            
        except Exception as e:
            logger.error(f"❌ Error enabling fast mode: {e}")
            conn.rollback()
        finally:
            conn.close()
    
    def disable_fast_mode(self):
        """Restore normal database settings"""
        logger.info("🔄 RESTORING NORMAL DATABASE SETTINGS")
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Re-enable foreign key checks
            cursor.execute("SET session_replication_role = DEFAULT;")
            
            # Restore normal settings
            cursor.execute("SET synchronous_commit = on;")
            cursor.execute("SET autovacuum = on;")
            
            # Reset work memory to defaults
            cursor.execute("RESET work_mem;")
            cursor.execute("RESET maintenance_work_mem;")
            
            conn.commit()
            logger.info("✅ Normal database settings restored")
            
        except Exception as e:
            logger.error(f"❌ Error restoring settings: {e}")
            conn.rollback()
        finally:
            conn.close()
    
    def drop_indexes_for_fast_load(self, schema_name):
        """Drop non-essential indexes for faster loading"""
        logger.info(f"🗑️ DROPPING NON-ESSENTIAL INDEXES IN {schema_name}")
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Get all indexes except primary keys
            cursor.execute("""
                SELECT schemaname, tablename, indexname 
                FROM pg_indexes 
                WHERE schemaname = %s 
                AND indexname NOT LIKE '%_pkey'
                AND indexname NOT LIKE 'pk_%'
            """, (schema_name,))
            
            indexes = cursor.fetchall()
            
            for schema, table, index in indexes:
                try:
                    cursor.execute(f'DROP INDEX IF EXISTS "{schema}"."{index}"')
                    logger.info(f"   Dropped index: {index}")
                except Exception as e:
                    logger.warning(f"   Could not drop {index}: {e}")
            
            conn.commit()
            logger.info(f"✅ Dropped {len(indexes)} indexes from {schema_name}")
            
        except Exception as e:
            logger.error(f"❌ Error dropping indexes: {e}")
            conn.rollback()
        finally:
            conn.close()
    
    def create_essential_indexes(self, schema_name):
        """Create only essential indexes after ETL"""
        logger.info(f"🔧 CREATING ESSENTIAL INDEXES IN {schema_name}")
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Essential indexes for common queries
            essential_indexes = [
                f'CREATE INDEX IF NOT EXISTS idx_{schema_name}_jiraissue_project ON "{schema_name}".jiraissue(project)',
                f'CREATE INDEX IF NOT EXISTS idx_{schema_name}_jiraissue_status ON "{schema_name}".jiraissue(issuestatus)',
                f'CREATE INDEX IF NOT EXISTS idx_{schema_name}_worklog_issue ON "{schema_name}".worklog(issueid)',
                f'CREATE INDEX IF NOT EXISTS idx_{schema_name}_customfieldvalue_issue ON "{schema_name}".customfieldvalue(issue)',
            ]
            
            for index_sql in essential_indexes:
                try:
                    cursor.execute(index_sql)
                    logger.info(f"   Created essential index")
                except Exception as e:
                    logger.warning(f"   Could not create index: {e}")
            
            conn.commit()
            logger.info(f"✅ Created essential indexes in {schema_name}")
            
        except Exception as e:
            logger.error(f"❌ Error creating indexes: {e}")
            conn.rollback()
        finally:
            conn.close()

def main():
    """Main function for testing"""
    optimizer = FastModeOptimizer()
    
    # Enable fast mode
    optimizer.enable_fast_mode()
    
    # Drop indexes in staging for faster loading
    optimizer.drop_indexes_for_fast_load('staging')
    
    print("🚀 Fast mode enabled! Run your ETL now.")
    print("⚠️  Remember to run disable_fast_mode() after ETL completion")

if __name__ == "__main__":
    main()
