2025-06-17 13:34:05,629 - INFO - 
Insertion 32 dimensions
2025-06-17 13:34:05,629 - INFO - [ 1/44] project
2025-06-17 13:34:05,629 - INFO - Insertion dim_project
2025-06-17 13:34:05,664 - INFO -    SUCCES 19 enregistrements copies
2025-06-17 13:34:05,665 - INFO - [ 2/44] component
2025-06-17 13:34:05,665 - INFO - Insertion dim_component
2025-06-17 13:34:05,698 - INFO -    SUCCES 139 enregistrements copies
2025-06-17 13:34:05,698 - INFO - [ 3/44] customfield
2025-06-17 13:34:05,699 - INFO - Insertion dim_customfield
2025-06-17 13:34:05,760 - INFO -    SUCCES 32 enregistrements copies
2025-06-17 13:34:05,761 - INFO - [ 4/44] customfieldvalue
2025-06-17 13:34:05,761 - INFO - Insertion dim_customfieldvalue
2025-06-17 13:34:05,854 - INFO -    SUCCES 10,317 enregistrements copies
2025-06-17 13:34:05,854 - INFO - [ 5/44] worklog
2025-06-17 13:34:05,854 - INFO - Insertion dim_worklog
2025-06-17 13:34:05,970 - INFO -    SUCCES 6,315 enregistrements copies
2025-06-17 13:34:05,971 - INFO - [ 6/44] fileattachment
2025-06-17 13:34:05,971 - INFO - Insertion dim_fileattachment
2025-06-17 13:34:06,036 - INFO -    SUCCES 9 enregistrements copies
2025-06-17 13:34:06,037 - INFO - [ 7/44] issuelinktype
2025-06-17 13:34:06,037 - INFO - Insertion dim_issuelinktype
2025-06-17 13:34:06,080 - INFO -    SUCCES 7 enregistrements copies
2025-06-17 13:34:06,080 - INFO - [ 8/44] label
2025-06-17 13:34:06,080 - INFO - Insertion dim_label
2025-06-17 13:34:06,137 - INFO -    SUCCES 3,400 enregistrements copies
2025-06-17 13:34:06,137 - INFO - [ 9/44] cwd_group
2025-06-17 13:34:06,138 - INFO - Insertion dim_cwd_group
2025-06-17 13:34:06,207 - INFO -    SUCCES 3 enregistrements copies
2025-06-17 13:34:06,208 - INFO - [10/44] cwd_user
2025-06-17 13:34:06,208 - INFO - Insertion dim_cwd_user
2025-06-17 13:34:06,251 - INFO -    SUCCES 49 enregistrements copies
2025-06-17 13:34:06,252 - INFO - [11/44] cwd_membership
2025-06-17 13:34:06,252 - INFO - Insertion dim_cwd_membership
2025-06-17 13:34:06,311 - INFO -    SUCCES 50 enregistrements copies
2025-06-17 13:34:06,312 - INFO - [12/44] projectroleactor
2025-06-17 13:34:06,312 - INFO - Insertion dim_projectroleactor
2025-06-17 13:34:06,364 - INFO -    SUCCES 38 enregistrements copies
2025-06-17 13:34:06,364 - INFO - [13/44] jiraworkflows
2025-06-17 13:34:06,364 - INFO - Insertion dim_jiraworkflows
2025-06-17 13:34:06,404 - INFO -    SUCCES 5 enregistrements copies
2025-06-17 13:34:06,404 - INFO - [14/44] workflowscheme
2025-06-17 13:34:06,405 - INFO - Insertion dim_workflowscheme
2025-06-17 13:34:06,483 - INFO -    SUCCES 4 enregistrements copies
2025-06-17 13:34:06,483 - INFO - [15/44] workflowschemeentity
2025-06-17 13:34:06,483 - INFO - Insertion dim_workflowschemeentity
2025-06-17 13:34:06,527 - INFO -    SUCCES 4 enregistrements copies
2025-06-17 13:34:06,527 - INFO - [16/44] fieldconfigscheme
2025-06-17 13:34:06,527 - INFO - Insertion dim_fieldconfigscheme
2025-06-17 13:34:06,572 - INFO -    SUCCES 38 enregistrements copies
2025-06-17 13:34:06,572 - INFO - [17/44] fieldconfiguration
2025-06-17 13:34:06,573 - INFO - Insertion dim_fieldconfiguration
2025-06-17 13:34:06,618 - INFO -    SUCCES 38 enregistrements copies
2025-06-17 13:34:06,619 - INFO - [18/44] fieldscreen
2025-06-17 13:34:06,619 - INFO - Insertion dim_fieldscreen
2025-06-17 13:34:06,667 - INFO -    SUCCES 10 enregistrements copies
2025-06-17 13:34:06,667 - INFO - [19/44] fieldscreentab
2025-06-17 13:34:06,667 - INFO - Insertion dim_fieldscreentab
2025-06-17 13:34:06,711 - INFO -    SUCCES 10 enregistrements copies
2025-06-17 13:34:06,711 - INFO - [20/44] permissionscheme
2025-06-17 13:34:06,711 - INFO - Insertion dim_permissionscheme
2025-06-17 13:34:06,738 - INFO -    SUCCES 2 enregistrements copies
2025-06-17 13:34:06,739 - INFO - [21/44] schemepermissions
2025-06-17 13:34:06,739 - INFO - Insertion dim_schemepermissions
2025-06-17 13:34:06,792 - INFO -    SUCCES 70 enregistrements copies
2025-06-17 13:34:06,792 - INFO - [22/44] priority
2025-06-17 13:34:06,792 - INFO - Insertion dim_priority
2025-06-17 13:34:06,835 - INFO -    SUCCES 7 enregistrements copies
2025-06-17 13:34:06,835 - INFO - [23/44] issuestatus
2025-06-17 13:34:06,835 - INFO - Insertion dim_issuestatus
2025-06-17 13:34:06,881 - INFO -    SUCCES 7 enregistrements copies
2025-06-17 13:34:06,881 - INFO - [24/44] resolution
2025-06-17 13:34:06,881 - INFO - Insertion dim_resolution
2025-06-17 13:34:06,926 - INFO -    SUCCES 8 enregistrements copies
2025-06-17 13:34:06,927 - INFO - [25/44] issuetype
2025-06-17 13:34:06,927 - INFO - Insertion dim_issuetype
2025-06-17 13:34:06,975 - INFO -    SUCCES 11 enregistrements copies
2025-06-17 13:34:06,975 - INFO - [26/44] projectrole
2025-06-17 13:34:06,976 - INFO - Insertion dim_projectrole
2025-06-17 13:34:07,018 - INFO -    SUCCES 2 enregistrements copies
2025-06-17 13:34:07,018 - INFO - [27/44] pluginversion
2025-06-17 13:34:07,018 - INFO - Insertion dim_pluginversion
2025-06-17 13:34:07,070 - INFO -    SUCCES 291 enregistrements copies
2025-06-17 13:34:07,070 - INFO - [28/44] managedconfigurationitem
2025-06-17 13:34:07,070 - INFO - Insertion dim_managedconfigurationitem
2025-06-17 13:34:07,111 - INFO -    SUCCES 17 enregistrements copies
2025-06-17 13:34:07,111 - INFO - [29/44] AO_60DB71_RAPIDVIEW
2025-06-17 13:34:07,111 - INFO - Insertion dim_AO_60DB71_RAPIDVIEW
2025-06-17 13:34:07,162 - INFO -    SUCCES 4 enregistrements copies
2025-06-17 13:34:07,162 - INFO - [30/44] AO_C77861_AUDIT_ENTITY
2025-06-17 13:34:07,162 - INFO - Insertion dim_AO_C77861_AUDIT_ENTITY
2025-06-17 13:34:07,218 - INFO -    SUCCES 836 enregistrements copies
2025-06-17 13:34:07,218 - INFO - [31/44] AO_C77861_AUDIT_ACTION_CACHE
2025-06-17 13:34:07,218 - INFO - Insertion dim_AO_C77861_AUDIT_ACTION_CACHE
2025-06-17 13:34:07,291 - INFO -    SUCCES 34 enregistrements copies
2025-06-17 13:34:07,291 - INFO - [32/44] AO_C77861_AUDIT_CATEGORY_CACHE
2025-06-17 13:34:07,291 - INFO - Insertion dim_AO_C77861_AUDIT_CATEGORY_CACHE
2025-06-17 13:34:07,346 - INFO -    SUCCES 15 enregistrements copies
2025-06-17 13:34:07,346 - INFO - 
Dimensions chargees: 32/44
2025-06-17 13:34:07,346 - INFO - 
Insertion FACT_JIRAISSUE
2025-06-17 13:34:07,386 - INFO -    Insertion de 19 colonnes jiraissue...
2025-06-17 13:34:07,466 - INFO -    SUCCES 2,335 enregistrements fact_jiraissue copies
2025-06-17 13:34:07,466 - INFO -    Colonnes jiraissue: 19
2025-06-17 13:34:07,466 - INFO -    Populating FK columns...
2025-06-17 13:34:07,466 - INFO - 
🔗 POPULATING FOREIGN KEYS IN FACT TABLE
2025-06-17 13:34:07,484 - INFO -    Found 49 columns in fact_jiraissue
2025-06-17 13:34:07,484 - INFO -    ✅ Direct: project -> dim_project
2025-06-17 13:34:07,484 - INFO -    ✅ Direct: component -> dim_component
2025-06-17 13:34:07,484 - INFO -    ✅ Direct: assignee -> dim_cwd_user
2025-06-17 13:34:07,484 - INFO -    ✅ Direct: reporter -> dim_cwd_user
2025-06-17 13:34:07,484 - INFO -    ✅ Direct: creator -> dim_cwd_user
2025-06-17 13:34:07,486 - INFO -    ✅ Direct: priority -> dim_priority
2025-06-17 13:34:07,486 - INFO -    ✅ Direct: resolution -> dim_resolution
2025-06-17 13:34:07,486 - INFO -    ✅ Direct: issuestatus -> dim_issuestatus
2025-06-17 13:34:07,486 - INFO -    ✅ Direct: issuetype -> dim_issuetype
2025-06-17 13:34:07,486 - INFO -    ✅ Business Logic: workflow -> dim_jiraworkflows
2025-06-17 13:34:07,486 - INFO -    ✅ Business Logic: customfield -> dim_customfield
2025-06-17 13:34:07,486 - INFO -    ✅ Business Logic: customfieldvalue -> dim_customfieldvalue
2025-06-17 13:34:07,486 - INFO -    ✅ Business Logic: worklog -> dim_worklog
2025-06-17 13:34:07,486 - INFO -    ✅ Business Logic: fileattachment -> dim_fileattachment
2025-06-17 13:34:07,486 - INFO -    ✅ Business Logic: issuelinktype -> dim_issuelinktype
2025-06-17 13:34:07,486 - INFO -    ✅ Business Logic: label -> dim_label
2025-06-17 13:34:07,486 - INFO -    ✅ Business Logic: cwd_group -> dim_cwd_group
2025-06-17 13:34:07,486 - INFO -    ✅ Business Logic: cwd_membership -> dim_cwd_membership
2025-06-17 13:34:07,486 - INFO -    ✅ Business Logic: projectroleactor -> dim_projectroleactor
2025-06-17 13:34:07,486 - INFO -    ✅ Business Logic: jiraworkflows -> dim_jiraworkflows
2025-06-17 13:34:07,486 - INFO -    ✅ Business Logic: workflowscheme -> dim_workflowscheme
2025-06-17 13:34:07,486 - INFO -    ✅ Business Logic: workflowschemeentity -> dim_workflowschemeentity
2025-06-17 13:34:07,486 - INFO -    ✅ Business Logic: fieldconfigscheme -> dim_fieldconfigscheme
2025-06-17 13:34:07,486 - INFO -    ✅ Business Logic: fieldconfiguration -> dim_fieldconfiguration
2025-06-17 13:34:07,486 - INFO -    ✅ Business Logic: fieldscreen -> dim_fieldscreen
2025-06-17 13:34:07,486 - INFO -    ✅ Business Logic: fieldscreentab -> dim_fieldscreentab
2025-06-17 13:34:07,486 - INFO -    ✅ Business Logic: permissionscheme -> dim_permissionscheme
2025-06-17 13:34:07,487 - INFO -    ✅ Business Logic: schemepermissions -> dim_schemepermissions
2025-06-17 13:34:07,487 - INFO -    ✅ Business Logic: projectrole -> dim_projectrole
2025-06-17 13:34:07,487 - INFO -    ✅ Business Logic: pluginversion -> dim_pluginversion
2025-06-17 13:34:07,487 - INFO -    ✅ Business Logic: managedconfigurationitem -> dim_managedconfigurationitem
2025-06-17 13:34:07,487 - INFO -    ✅ Business Logic: AO_60DB71_RAPIDVIEW -> dim_AO_60DB71_RAPIDVIEW
2025-06-17 13:34:07,487 - INFO -    ✅ Business Logic: AO_C77861_AUDIT_ENTITY -> dim_AO_C77861_AUDIT_ENTITY
2025-06-17 13:34:07,487 - INFO -    ✅ Business Logic: AO_C77861_AUDIT_ACTION_CACHE -> dim_AO_C77861_AUDIT_ACTION_CACHE
2025-06-17 13:34:07,487 - INFO -    ✅ Business Logic: AO_C77861_AUDIT_CATEGORY_CACHE -> dim_AO_C77861_AUDIT_CATEGORY_CACHE
2025-06-17 13:34:07,487 - INFO -    Total FK relationships to populate: 35
2025-06-17 13:34:07,487 - INFO -    Populating project_id from jiraissue.project
2025-06-17 13:34:07,494 - INFO -      Dimension project: 19 records, Fact non-null project: 2335
2025-06-17 13:34:07,529 - INFO -      ✅ Updated 2,335 rows for project_id (Total populated: 2,335)
2025-06-17 13:34:07,529 - INFO -    Populating component_id from jiraissue.component
2025-06-17 13:34:07,536 - INFO -      Dimension component: 139 records, Fact non-null component: 0
2025-06-17 13:34:07,536 - WARNING -      No non-null values in fact_jiraissue.component
2025-06-17 13:34:07,536 - INFO -    Populating cwd_user_id from jiraissue.assignee
2025-06-17 13:34:07,539 - ERROR -      ❌ Error populating cwd_user_id: operator does not exist: numeric = character varying
LINE 5: ...             WHERE load.fact_jiraissue."assignee" = u.user_n...
                                                             ^
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.

2025-06-17 13:34:07,540 - INFO -    Populating cwd_user_id from jiraissue.reporter
2025-06-17 13:34:07,542 - ERROR -      ❌ Error populating cwd_user_id: operator does not exist: numeric = character varying
LINE 5: ...             WHERE load.fact_jiraissue."reporter" = u.user_n...
                                                             ^
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.

2025-06-17 13:34:07,543 - INFO -    Populating cwd_user_id from jiraissue.creator
2025-06-17 13:34:07,546 - ERROR -      ❌ Error populating cwd_user_id: operator does not exist: numeric = character varying
LINE 5: ...              WHERE load.fact_jiraissue."creator" = u.user_n...
                                                             ^
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.

2025-06-17 13:34:07,547 - INFO -    Populating priority_id from jiraissue.priority
2025-06-17 13:34:07,553 - INFO -      Dimension priority: 7 records, Fact non-null priority: 2335
2025-06-17 13:34:07,596 - INFO -      ✅ Updated 2,335 rows for priority_id (Total populated: 2,335)
2025-06-17 13:34:07,596 - INFO -    Populating resolution_id from jiraissue.resolution
2025-06-17 13:34:07,604 - INFO -      Dimension resolution: 8 records, Fact non-null resolution: 1420
2025-06-17 13:34:07,633 - INFO -      ✅ Updated 1,420 rows for resolution_id (Total populated: 1,420)
2025-06-17 13:34:07,633 - INFO -    Populating issuestatus_id from jiraissue.issuestatus
2025-06-17 13:34:07,641 - INFO -      Dimension issuestatus: 7 records, Fact non-null issuestatus: 2335
2025-06-17 13:34:07,679 - INFO -      ✅ Updated 2,335 rows for issuestatus_id (Total populated: 2,335)
2025-06-17 13:34:07,679 - INFO -    Populating issuetype_id from jiraissue.issuetype
2025-06-17 13:34:07,686 - INFO -      Dimension issuetype: 11 records, Fact non-null issuetype: 2335
2025-06-17 13:34:07,727 - INFO -      ✅ Updated 2,335 rows for issuetype_id (Total populated: 2,335)
2025-06-17 13:34:07,728 - INFO -    Populating jiraworkflows_id from jiraissue.workflow
2025-06-17 13:34:07,730 - INFO -      Column workflow not found - using business logic (DISABLED)
2025-06-17 13:34:07,730 - INFO -      Skipping fallback FK population for jiraworkflows_id (dim_jiraworkflows) - only real matches will be set.
2025-06-17 13:34:07,730 - INFO -    Populating customfield_id from jiraissue.customfield
2025-06-17 13:34:07,732 - INFO -      Column customfield not found - using business logic (DISABLED)
2025-06-17 13:34:07,732 - INFO -      Skipping fallback FK population for customfield_id (dim_customfield) - only real matches will be set.
2025-06-17 13:34:07,732 - INFO -    Populating customfieldvalue_id from jiraissue.customfieldvalue
2025-06-17 13:34:07,734 - INFO -      Column customfieldvalue not found - using business logic (DISABLED)
2025-06-17 13:34:07,734 - INFO -      Skipping fallback FK population for customfieldvalue_id (dim_customfieldvalue) - only real matches will be set.
2025-06-17 13:34:07,734 - INFO -    Populating worklog_id from jiraissue.worklog
2025-06-17 13:34:07,736 - INFO -      Column worklog not found - using business logic (DISABLED)
2025-06-17 13:34:07,736 - INFO -      Skipping fallback FK population for worklog_id (dim_worklog) - only real matches will be set.
2025-06-17 13:34:07,736 - INFO -    Populating fileattachment_id from jiraissue.fileattachment
2025-06-17 13:34:07,738 - INFO -      Column fileattachment not found - using business logic (DISABLED)
2025-06-17 13:34:07,738 - INFO -      Skipping fallback FK population for fileattachment_id (dim_fileattachment) - only real matches will be set.
2025-06-17 13:34:07,738 - INFO -    Populating issuelinktype_id from jiraissue.issuelinktype
2025-06-17 13:34:07,740 - INFO -      Column issuelinktype not found - using business logic (DISABLED)
2025-06-17 13:34:07,740 - INFO -      Skipping fallback FK population for issuelinktype_id (dim_issuelinktype) - only real matches will be set.
2025-06-17 13:34:07,740 - INFO -    Populating label_id from jiraissue.label
2025-06-17 13:34:07,741 - INFO -      Column label not found - using business logic (DISABLED)
2025-06-17 13:34:07,741 - INFO -      Skipping fallback FK population for label_id (dim_label) - only real matches will be set.
2025-06-17 13:34:07,741 - INFO -    Populating cwd_group_id from jiraissue.cwd_group
2025-06-17 13:34:07,743 - INFO -      Column cwd_group not found - using business logic (DISABLED)
2025-06-17 13:34:07,743 - INFO -      Skipping fallback FK population for cwd_group_id (dim_cwd_group) - only real matches will be set.
2025-06-17 13:34:07,743 - INFO -    Populating cwd_membership_id from jiraissue.cwd_membership
2025-06-17 13:34:07,746 - INFO -      Column cwd_membership not found - using business logic (DISABLED)
2025-06-17 13:34:07,746 - INFO -      Skipping fallback FK population for cwd_membership_id (dim_cwd_membership) - only real matches will be set.
2025-06-17 13:34:07,746 - INFO -    Populating projectroleactor_id from jiraissue.projectroleactor
2025-06-17 13:34:07,748 - INFO -      Column projectroleactor not found - using business logic (DISABLED)
2025-06-17 13:34:07,748 - INFO -      Skipping fallback FK population for projectroleactor_id (dim_projectroleactor) - only real matches will be set.
2025-06-17 13:34:07,748 - INFO -    Populating jiraworkflows_id from jiraissue.jiraworkflows
2025-06-17 13:34:07,750 - INFO -      Column jiraworkflows not found - using business logic (DISABLED)
2025-06-17 13:34:07,750 - INFO -      Skipping fallback FK population for jiraworkflows_id (dim_jiraworkflows) - only real matches will be set.
2025-06-17 13:34:07,750 - INFO -    Populating workflowscheme_id from jiraissue.workflowscheme
2025-06-17 13:34:07,751 - INFO -      Column workflowscheme not found - using business logic (DISABLED)
2025-06-17 13:34:07,752 - INFO -      Skipping fallback FK population for workflowscheme_id (dim_workflowscheme) - only real matches will be set.
2025-06-17 13:34:07,752 - INFO -    Populating workflowschemeentity_id from jiraissue.workflowschemeentity
2025-06-17 13:34:07,753 - INFO -      Column workflowschemeentity not found - using business logic (DISABLED)
2025-06-17 13:34:07,753 - INFO -      Skipping fallback FK population for workflowschemeentity_id (dim_workflowschemeentity) - only real matches will be set.
2025-06-17 13:34:07,754 - INFO -    Populating fieldconfigscheme_id from jiraissue.fieldconfigscheme
2025-06-17 13:34:07,755 - INFO -      Column fieldconfigscheme not found - using business logic (DISABLED)
2025-06-17 13:34:07,755 - INFO -      Skipping fallback FK population for fieldconfigscheme_id (dim_fieldconfigscheme) - only real matches will be set.
2025-06-17 13:34:07,755 - INFO -    Populating fieldconfiguration_id from jiraissue.fieldconfiguration
2025-06-17 13:34:07,757 - INFO -      Column fieldconfiguration not found - using business logic (DISABLED)
2025-06-17 13:34:07,757 - INFO -      Skipping fallback FK population for fieldconfiguration_id (dim_fieldconfiguration) - only real matches will be set.
2025-06-17 13:34:07,757 - INFO -    Populating fieldscreen_id from jiraissue.fieldscreen
2025-06-17 13:34:07,759 - INFO -      Column fieldscreen not found - using business logic (DISABLED)
2025-06-17 13:34:07,759 - INFO -      Skipping fallback FK population for fieldscreen_id (dim_fieldscreen) - only real matches will be set.
2025-06-17 13:34:07,759 - INFO -    Populating fieldscreentab_id from jiraissue.fieldscreentab
2025-06-17 13:34:07,761 - INFO -      Column fieldscreentab not found - using business logic (DISABLED)
2025-06-17 13:34:07,761 - INFO -      Skipping fallback FK population for fieldscreentab_id (dim_fieldscreentab) - only real matches will be set.
2025-06-17 13:34:07,761 - INFO -    Populating permissionscheme_id from jiraissue.permissionscheme
2025-06-17 13:34:07,762 - INFO -      Column permissionscheme not found - using business logic (DISABLED)
2025-06-17 13:34:07,763 - INFO -      Skipping fallback FK population for permissionscheme_id (dim_permissionscheme) - only real matches will be set.
2025-06-17 13:34:07,763 - INFO -    Populating schemepermissions_id from jiraissue.schemepermissions
2025-06-17 13:34:07,764 - INFO -      Column schemepermissions not found - using business logic (DISABLED)
2025-06-17 13:34:07,764 - INFO -      Skipping fallback FK population for schemepermissions_id (dim_schemepermissions) - only real matches will be set.
2025-06-17 13:34:07,764 - INFO -    Populating projectrole_id from jiraissue.projectrole
2025-06-17 13:34:07,765 - INFO -      Column projectrole not found - using business logic (DISABLED)
2025-06-17 13:34:07,765 - INFO -      Skipping fallback FK population for projectrole_id (dim_projectrole) - only real matches will be set.
2025-06-17 13:34:07,765 - INFO -    Populating pluginversion_id from jiraissue.pluginversion
2025-06-17 13:34:07,767 - INFO -      Column pluginversion not found - using business logic (DISABLED)
2025-06-17 13:34:07,767 - INFO -      Skipping fallback FK population for pluginversion_id (dim_pluginversion) - only real matches will be set.
2025-06-17 13:34:07,767 - INFO -    Populating managedconfigurationitem_id from jiraissue.managedconfigurationitem
2025-06-17 13:34:07,769 - INFO -      Column managedconfigurationitem not found - using business logic (DISABLED)
2025-06-17 13:34:07,770 - INFO -      Skipping fallback FK population for managedconfigurationitem_id (dim_managedconfigurationitem) - only real matches will be set.
2025-06-17 13:34:07,770 - INFO -    Populating AO_60DB71_RAPIDVIEW_id from jiraissue.AO_60DB71_RAPIDVIEW
2025-06-17 13:34:07,771 - INFO -      Column AO_60DB71_RAPIDVIEW not found - using business logic (DISABLED)
2025-06-17 13:34:07,771 - INFO -      Skipping fallback FK population for AO_60DB71_RAPIDVIEW_id (dim_AO_60DB71_RAPIDVIEW) - only real matches will be set.
2025-06-17 13:34:07,771 - INFO -    Populating AO_C77861_AUDIT_ENTITY_id from jiraissue.AO_C77861_AUDIT_ENTITY
2025-06-17 13:34:07,774 - INFO -      Column AO_C77861_AUDIT_ENTITY not found - using business logic (DISABLED)
2025-06-17 13:34:07,774 - INFO -      Skipping fallback FK population for AO_C77861_AUDIT_ENTITY_id (dim_AO_C77861_AUDIT_ENTITY) - only real matches will be set.
2025-06-17 13:34:07,774 - INFO -    Populating AO_C77861_AUDIT_ACTION_CACHE_id from jiraissue.AO_C77861_AUDIT_ACTION_CACHE
2025-06-17 13:34:07,776 - INFO -      Column AO_C77861_AUDIT_ACTION_CACHE not found - using business logic (DISABLED)
2025-06-17 13:34:07,776 - INFO -      Skipping fallback FK population for AO_C77861_AUDIT_ACTION_CACHE_id (dim_AO_C77861_AUDIT_ACTION_CACHE) - only real matches will be set.
2025-06-17 13:34:07,776 - INFO -    Populating AO_C77861_AUDIT_CATEGORY_CACHE_id from jiraissue.AO_C77861_AUDIT_CATEGORY_CACHE
2025-06-17 13:34:07,777 - INFO -      Column AO_C77861_AUDIT_CATEGORY_CACHE not found - using business logic (DISABLED)
2025-06-17 13:34:07,777 - INFO -      Skipping fallback FK population for AO_C77861_AUDIT_CATEGORY_CACHE_id (dim_AO_C77861_AUDIT_CATEGORY_CACHE) - only real matches will be set.
2025-06-17 13:34:07,777 - INFO - 🎯 Foreign keys successfully populated: 5/35
2025-06-17 13:34:07,816 - INFO - 
📊 FK POPULATION SUMMARY:
2025-06-17 13:34:07,816 - INFO -    ❌ AO_60DB71_RAPIDVIEW_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ AO_C77861_AUDIT_ACTION_CACHE_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ AO_C77861_AUDIT_CATEGORY_CACHE_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ AO_C77861_AUDIT_ENTITY_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ component_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ customfield_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ customfieldvalue_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ cwd_group_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ cwd_membership_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ cwd_user_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ fieldconfigscheme_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ fieldconfiguration_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ fieldscreen_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ fieldscreentab_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ fileattachment_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ issuelinktype_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ✅ issuestatus_id: 2,335 populated
2025-06-17 13:34:07,817 - INFO -    ✅ issuetype_id: 2,335 populated
2025-06-17 13:34:07,817 - INFO -    ❌ jiraworkflows_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ label_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ managedconfigurationitem_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ permissionscheme_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ pluginversion_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ✅ priority_id: 2,335 populated
2025-06-17 13:34:07,817 - INFO -    ✅ project_id: 2,335 populated
2025-06-17 13:34:07,817 - INFO -    ❌ projectrole_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ projectroleactor_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ✅ resolution_id: 1,420 populated
2025-06-17 13:34:07,817 - INFO -    ❌ schemepermissions_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ workflow_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ workflowscheme_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ workflowschemeentity_id: 0 populated
2025-06-17 13:34:07,817 - INFO -    ❌ worklog_id: 0 populated
2025-06-17 13:34:07,818 - INFO -    FK colonnes populated: 5
2025-06-17 13:34:07,818 - INFO - 
INSERTION STAR SCHEMA TERMINEE!
2025-06-17 13:34:07,818 - INFO - Duree: 2.19 secondes
2025-06-17 13:34:07,818 - INFO - Dimensions chargees: 32/31
2025-06-17 13:34:07,818 - INFO - Fact records: 2,335
2025-06-17 13:34:07,818 - INFO - Total enregistrements: 24,126
2025-06-17 13:34:07,818 - INFO - Erreurs: 0
2025-06-17 13:34:07,820 - INFO - 
STAR SCHEMA INSERTION REUSSIE!
2025-06-17 13:34:07,820 - INFO -    32 dimensions chargees
2025-06-17 13:34:07,820 - INFO -    2,335 fact records
2025-06-17 13:34:07,820 - INFO -    Architecture star schema complete
2025-06-17 13:34:07,820 - INFO - 
PROCHAINE ETAPE:
2025-06-17 13:34:07,820 - INFO -    Executer: python 03_load_validation.py
2025-06-17 14:10:53,303 - INFO - 
Insertion 32 dimensions
2025-06-17 14:10:53,305 - INFO - [ 1/44] project
2025-06-17 14:10:53,305 - INFO - Insertion dim_project
2025-06-17 14:10:53,351 - INFO -    SUCCES 19 enregistrements copies
2025-06-17 14:10:53,352 - INFO - [ 2/44] component
2025-06-17 14:10:53,352 - INFO - Insertion dim_component
2025-06-17 14:10:53,395 - INFO -    SUCCES 139 enregistrements copies
2025-06-17 14:10:53,395 - INFO - [ 3/44] customfield
2025-06-17 14:10:53,395 - INFO - Insertion dim_customfield
2025-06-17 14:10:53,424 - INFO -    SUCCES 32 enregistrements copies
2025-06-17 14:10:53,424 - INFO - [ 4/44] customfieldvalue
2025-06-17 14:10:53,424 - INFO - Insertion dim_customfieldvalue
2025-06-17 14:10:53,485 - INFO -    SUCCES 10,317 enregistrements copies
2025-06-17 14:10:53,486 - INFO - [ 5/44] worklog
2025-06-17 14:10:53,486 - INFO - Insertion dim_worklog
2025-06-17 14:10:53,526 - INFO -    SUCCES 6,315 enregistrements copies
2025-06-17 14:10:53,527 - INFO - [ 6/44] fileattachment
2025-06-17 14:10:53,527 - INFO - Insertion dim_fileattachment
2025-06-17 14:10:53,569 - INFO -    SUCCES 9 enregistrements copies
2025-06-17 14:10:53,569 - INFO - [ 7/44] issuelinktype
2025-06-17 14:10:53,569 - INFO - Insertion dim_issuelinktype
2025-06-17 14:10:53,617 - INFO -    SUCCES 7 enregistrements copies
2025-06-17 14:10:53,617 - INFO - [ 8/44] label
2025-06-17 14:10:53,617 - INFO - Insertion dim_label
2025-06-17 14:10:53,670 - INFO -    SUCCES 3,400 enregistrements copies
2025-06-17 14:10:53,670 - INFO - [ 9/44] cwd_group
2025-06-17 14:10:53,670 - INFO - Insertion dim_cwd_group
2025-06-17 14:10:53,707 - INFO -    SUCCES 3 enregistrements copies
2025-06-17 14:10:53,707 - INFO - [10/44] cwd_user
2025-06-17 14:10:53,707 - INFO - Insertion dim_cwd_user
2025-06-17 14:10:53,754 - INFO -    SUCCES 49 enregistrements copies
2025-06-17 14:10:53,754 - INFO - [11/44] cwd_membership
2025-06-17 14:10:53,756 - INFO - Insertion dim_cwd_membership
2025-06-17 14:10:53,825 - INFO -    SUCCES 50 enregistrements copies
2025-06-17 14:10:53,825 - INFO - [12/44] projectroleactor
2025-06-17 14:10:53,825 - INFO - Insertion dim_projectroleactor
2025-06-17 14:10:53,892 - INFO -    SUCCES 38 enregistrements copies
2025-06-17 14:10:53,892 - INFO - [13/44] jiraworkflows
2025-06-17 14:10:53,893 - INFO - Insertion dim_jiraworkflows
2025-06-17 14:10:53,944 - INFO -    SUCCES 5 enregistrements copies
2025-06-17 14:10:53,945 - INFO - [14/44] workflowscheme
2025-06-17 14:10:53,945 - INFO - Insertion dim_workflowscheme
2025-06-17 14:10:53,982 - INFO -    SUCCES 4 enregistrements copies
2025-06-17 14:10:53,982 - INFO - [15/44] workflowschemeentity
2025-06-17 14:10:53,982 - INFO - Insertion dim_workflowschemeentity
2025-06-17 14:10:54,041 - INFO -    SUCCES 4 enregistrements copies
2025-06-17 14:10:54,042 - INFO - [16/44] fieldconfigscheme
2025-06-17 14:10:54,042 - INFO - Insertion dim_fieldconfigscheme
2025-06-17 14:10:54,111 - INFO -    SUCCES 38 enregistrements copies
2025-06-17 14:10:54,111 - INFO - [17/44] fieldconfiguration
2025-06-17 14:10:54,112 - INFO - Insertion dim_fieldconfiguration
2025-06-17 14:10:54,173 - INFO -    SUCCES 38 enregistrements copies
2025-06-17 14:10:54,174 - INFO - [18/44] fieldscreen
2025-06-17 14:10:54,174 - INFO - Insertion dim_fieldscreen
2025-06-17 14:10:54,224 - INFO -    SUCCES 10 enregistrements copies
2025-06-17 14:10:54,224 - INFO - [19/44] fieldscreentab
2025-06-17 14:10:54,224 - INFO - Insertion dim_fieldscreentab
2025-06-17 14:10:54,297 - INFO -    SUCCES 10 enregistrements copies
2025-06-17 14:10:54,299 - INFO - [20/44] permissionscheme
2025-06-17 14:10:54,299 - INFO - Insertion dim_permissionscheme
2025-06-17 14:10:54,343 - INFO -    SUCCES 2 enregistrements copies
2025-06-17 14:10:54,344 - INFO - [21/44] schemepermissions
2025-06-17 14:10:54,347 - INFO - Insertion dim_schemepermissions
2025-06-17 14:10:54,420 - INFO -    SUCCES 70 enregistrements copies
2025-06-17 14:10:54,421 - INFO - [22/44] priority
2025-06-17 14:10:54,421 - INFO - Insertion dim_priority
2025-06-17 14:10:54,477 - INFO -    SUCCES 7 enregistrements copies
2025-06-17 14:10:54,477 - INFO - [23/44] issuestatus
2025-06-17 14:10:54,477 - INFO - Insertion dim_issuestatus
2025-06-17 14:10:54,523 - INFO -    SUCCES 7 enregistrements copies
2025-06-17 14:10:54,523 - INFO - [24/44] resolution
2025-06-17 14:10:54,524 - INFO - Insertion dim_resolution
2025-06-17 14:10:54,560 - INFO -    SUCCES 8 enregistrements copies
2025-06-17 14:10:54,561 - INFO - [25/44] issuetype
2025-06-17 14:10:54,561 - INFO - Insertion dim_issuetype
2025-06-17 14:10:54,609 - INFO -    SUCCES 11 enregistrements copies
2025-06-17 14:10:54,610 - INFO - [26/44] projectrole
2025-06-17 14:10:54,610 - INFO - Insertion dim_projectrole
2025-06-17 14:10:54,662 - INFO -    SUCCES 2 enregistrements copies
2025-06-17 14:10:54,662 - INFO - [27/44] pluginversion
2025-06-17 14:10:54,663 - INFO - Insertion dim_pluginversion
2025-06-17 14:10:54,703 - INFO -    SUCCES 0 enregistrements copies
2025-06-17 14:10:54,703 - INFO - [28/44] managedconfigurationitem
2025-06-17 14:10:54,703 - INFO - Insertion dim_managedconfigurationitem
2025-06-17 14:10:54,754 - INFO -    SUCCES 17 enregistrements copies
2025-06-17 14:10:54,755 - INFO - [29/44] AO_60DB71_RAPIDVIEW
2025-06-17 14:10:54,755 - INFO - Insertion dim_AO_60DB71_RAPIDVIEW
2025-06-17 14:10:54,827 - INFO -    SUCCES 0 enregistrements copies
2025-06-17 14:10:54,827 - INFO - [30/44] AO_C77861_AUDIT_ENTITY
2025-06-17 14:10:54,828 - INFO - Insertion dim_AO_C77861_AUDIT_ENTITY
2025-06-17 14:10:54,891 - INFO -    SUCCES 836 enregistrements copies
2025-06-17 14:10:54,892 - INFO - [31/44] AO_C77861_AUDIT_ACTION_CACHE
2025-06-17 14:10:54,892 - INFO - Insertion dim_AO_C77861_AUDIT_ACTION_CACHE
2025-06-17 14:10:54,960 - INFO -    SUCCES 34 enregistrements copies
2025-06-17 14:10:54,961 - INFO - [32/44] AO_C77861_AUDIT_CATEGORY_CACHE
2025-06-17 14:10:54,961 - INFO - Insertion dim_AO_C77861_AUDIT_CATEGORY_CACHE
2025-06-17 14:10:55,023 - INFO -    SUCCES 15 enregistrements copies
2025-06-17 14:10:55,023 - INFO - 
Dimensions chargees: 30/44
2025-06-17 14:10:55,023 - INFO - 
Insertion FACT_JIRAISSUE
2025-06-17 14:10:55,080 - INFO -    Insertion de 19 colonnes jiraissue...
2025-06-17 14:10:55,166 - INFO -    SUCCES 2,335 enregistrements fact_jiraissue copies
2025-06-17 14:10:55,166 - INFO -    Colonnes jiraissue: 19
2025-06-17 14:10:55,166 - INFO -    Populating FK columns...
2025-06-17 14:10:55,166 - INFO - 
🔗 POPULATING FOREIGN KEYS IN FACT TABLE
2025-06-17 14:10:55,212 - INFO -    Found 49 columns in fact_jiraissue
2025-06-17 14:10:55,212 - INFO -    ✅ Direct: project -> dim_project
2025-06-17 14:10:55,212 - WARNING -    ❌ Skipping: component (column not found)
2025-06-17 14:10:55,212 - INFO -    ✅ Direct: assignee -> dim_cwd_user
2025-06-17 14:10:55,212 - INFO -    ✅ Direct: reporter -> dim_cwd_user
2025-06-17 14:10:55,212 - INFO -    ✅ Direct: creator -> dim_cwd_user
2025-06-17 14:10:55,212 - INFO -    ✅ Direct: priority -> dim_priority
2025-06-17 14:10:55,212 - INFO -    ✅ Direct: resolution -> dim_resolution
2025-06-17 14:10:55,213 - INFO -    ✅ Direct: issuestatus -> dim_issuestatus
2025-06-17 14:10:55,213 - WARNING -    ❌ Skipping: issuetype (column not found)
2025-06-17 14:10:55,213 - INFO -    ✅ Business Logic: workflow -> dim_jiraworkflows
2025-06-17 14:10:55,213 - INFO -    ✅ Business Logic: customfield -> dim_customfield
2025-06-17 14:10:55,213 - INFO -    ✅ Business Logic: customfieldvalue -> dim_customfieldvalue
2025-06-17 14:10:55,213 - INFO -    ✅ Business Logic: worklog -> dim_worklog
2025-06-17 14:10:55,213 - INFO -    ✅ Business Logic: fileattachment -> dim_fileattachment
2025-06-17 14:10:55,213 - INFO -    ✅ Business Logic: issuelinktype -> dim_issuelinktype
2025-06-17 14:10:55,213 - INFO -    ✅ Business Logic: label -> dim_label
2025-06-17 14:10:55,213 - INFO -    ✅ Business Logic: cwd_group -> dim_cwd_group
2025-06-17 14:10:55,213 - INFO -    ✅ Business Logic: cwd_membership -> dim_cwd_membership
2025-06-17 14:10:55,213 - INFO -    ✅ Business Logic: projectroleactor -> dim_projectroleactor
2025-06-17 14:10:55,213 - INFO -    ✅ Business Logic: jiraworkflows -> dim_jiraworkflows
2025-06-17 14:10:55,213 - INFO -    ✅ Business Logic: workflowscheme -> dim_workflowscheme
2025-06-17 14:10:55,213 - INFO -    ✅ Business Logic: workflowschemeentity -> dim_workflowschemeentity
2025-06-17 14:10:55,214 - INFO -    ✅ Business Logic: fieldconfigscheme -> dim_fieldconfigscheme
2025-06-17 14:10:55,214 - INFO -    ✅ Business Logic: fieldconfiguration -> dim_fieldconfiguration
2025-06-17 14:10:55,214 - INFO -    ✅ Business Logic: fieldscreen -> dim_fieldscreen
2025-06-17 14:10:55,214 - INFO -    ✅ Business Logic: fieldscreentab -> dim_fieldscreentab
2025-06-17 14:10:55,214 - INFO -    ✅ Business Logic: permissionscheme -> dim_permissionscheme
2025-06-17 14:10:55,214 - INFO -    ✅ Business Logic: schemepermissions -> dim_schemepermissions
2025-06-17 14:10:55,214 - INFO -    ✅ Business Logic: projectrole -> dim_projectrole
2025-06-17 14:10:55,214 - INFO -    ✅ Business Logic: pluginversion -> dim_pluginversion
2025-06-17 14:10:55,214 - INFO -    ✅ Business Logic: managedconfigurationitem -> dim_managedconfigurationitem
2025-06-17 14:10:55,214 - INFO -    ✅ Business Logic: AO_60DB71_RAPIDVIEW -> dim_AO_60DB71_RAPIDVIEW
2025-06-17 14:10:55,214 - INFO -    ✅ Business Logic: AO_C77861_AUDIT_ENTITY -> dim_AO_C77861_AUDIT_ENTITY
2025-06-17 14:10:55,214 - INFO -    ✅ Business Logic: AO_C77861_AUDIT_ACTION_CACHE -> dim_AO_C77861_AUDIT_ACTION_CACHE
2025-06-17 14:10:55,214 - INFO -    ✅ Business Logic: AO_C77861_AUDIT_CATEGORY_CACHE -> dim_AO_C77861_AUDIT_CATEGORY_CACHE
2025-06-17 14:10:55,214 - INFO -    Total FK relationships to populate: 33
2025-06-17 14:10:55,214 - INFO -    Populating project_id from jiraissue.project
2025-06-17 14:10:55,227 - INFO -      Dimension project: 19 records, Fact non-null project: 2335
2025-06-17 14:10:55,275 - INFO -      ✅ Updated 2,335 rows for project_id (Total populated: 2,335)
2025-06-17 14:10:55,276 - INFO -    Populating cwd_user_id from jiraissue.assignee
2025-06-17 14:10:55,280 - ERROR -      ❌ Error populating cwd_user_id: operator does not exist: numeric = character varying
LINE 5: ...             WHERE load.fact_jiraissue."assignee" = u.user_n...
                                                             ^
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.

2025-06-17 14:10:55,281 - INFO -    Populating cwd_user_id from jiraissue.reporter
2025-06-17 14:10:55,285 - ERROR -      ❌ Error populating cwd_user_id: operator does not exist: numeric = character varying
LINE 5: ...             WHERE load.fact_jiraissue."reporter" = u.user_n...
                                                             ^
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.

2025-06-17 14:10:55,286 - INFO -    Populating cwd_user_id from jiraissue.creator
2025-06-17 14:10:55,290 - ERROR -      ❌ Error populating cwd_user_id: operator does not exist: numeric = character varying
LINE 5: ...              WHERE load.fact_jiraissue."creator" = u.user_n...
                                                             ^
HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.

2025-06-17 14:10:55,291 - INFO -    Populating priority_id from jiraissue.priority
2025-06-17 14:10:55,299 - INFO -      Dimension priority: 7 records, Fact non-null priority: 2335
2025-06-17 14:10:55,345 - INFO -      ✅ Updated 2,335 rows for priority_id (Total populated: 2,335)
2025-06-17 14:10:55,345 - INFO -    Populating resolution_id from jiraissue.resolution
2025-06-17 14:10:55,355 - INFO -      Dimension resolution: 8 records, Fact non-null resolution: 1420
2025-06-17 14:10:55,394 - INFO -      ✅ Updated 1,420 rows for resolution_id (Total populated: 1,420)
2025-06-17 14:10:55,395 - INFO -    Populating issuestatus_id from jiraissue.issuestatus
2025-06-17 14:10:55,404 - INFO -      Dimension issuestatus: 7 records, Fact non-null issuestatus: 2335
2025-06-17 14:10:55,447 - INFO -      ✅ Updated 2,335 rows for issuestatus_id (Total populated: 2,335)
2025-06-17 14:10:55,447 - INFO -    Populating jiraworkflows_id from jiraissue.workflow
2025-06-17 14:10:55,450 - INFO -      Column workflow not found - using business logic (DISABLED)
2025-06-17 14:10:55,450 - INFO -      Skipping fallback FK population for jiraworkflows_id (dim_jiraworkflows) - only real matches will be set.
2025-06-17 14:10:55,450 - INFO -    Populating customfield_id from jiraissue.customfield
2025-06-17 14:10:55,452 - INFO -      Column customfield not found - using business logic (DISABLED)
2025-06-17 14:10:55,452 - INFO -      Skipping fallback FK population for customfield_id (dim_customfield) - only real matches will be set.
2025-06-17 14:10:55,453 - INFO -    Populating customfieldvalue_id from jiraissue.customfieldvalue
2025-06-17 14:10:55,455 - INFO -      Column customfieldvalue not found - using business logic (DISABLED)
2025-06-17 14:10:55,455 - INFO -      Skipping fallback FK population for customfieldvalue_id (dim_customfieldvalue) - only real matches will be set.
2025-06-17 14:10:55,455 - INFO -    Populating worklog_id from jiraissue.worklog
2025-06-17 14:10:55,459 - INFO -      Column worklog not found - using business logic (DISABLED)
2025-06-17 14:10:55,459 - INFO -      Skipping fallback FK population for worklog_id (dim_worklog) - only real matches will be set.
2025-06-17 14:10:55,459 - INFO -    Populating fileattachment_id from jiraissue.fileattachment
2025-06-17 14:10:55,461 - INFO -      Column fileattachment not found - using business logic (DISABLED)
2025-06-17 14:10:55,461 - INFO -      Skipping fallback FK population for fileattachment_id (dim_fileattachment) - only real matches will be set.
2025-06-17 14:10:55,461 - INFO -    Populating issuelinktype_id from jiraissue.issuelinktype
2025-06-17 14:10:55,463 - INFO -      Column issuelinktype not found - using business logic (DISABLED)
2025-06-17 14:10:55,463 - INFO -      Skipping fallback FK population for issuelinktype_id (dim_issuelinktype) - only real matches will be set.
2025-06-17 14:10:55,463 - INFO -    Populating label_id from jiraissue.label
2025-06-17 14:10:55,466 - INFO -      Column label not found - using business logic (DISABLED)
2025-06-17 14:10:55,467 - INFO -      Skipping fallback FK population for label_id (dim_label) - only real matches will be set.
2025-06-17 14:10:55,467 - INFO -    Populating cwd_group_id from jiraissue.cwd_group
2025-06-17 14:10:55,469 - INFO -      Column cwd_group not found - using business logic (DISABLED)
2025-06-17 14:10:55,469 - INFO -      Skipping fallback FK population for cwd_group_id (dim_cwd_group) - only real matches will be set.
2025-06-17 14:10:55,469 - INFO -    Populating cwd_membership_id from jiraissue.cwd_membership
2025-06-17 14:10:55,472 - INFO -      Column cwd_membership not found - using business logic (DISABLED)
2025-06-17 14:10:55,472 - INFO -      Skipping fallback FK population for cwd_membership_id (dim_cwd_membership) - only real matches will be set.
2025-06-17 14:10:55,472 - INFO -    Populating projectroleactor_id from jiraissue.projectroleactor
2025-06-17 14:10:55,475 - INFO -      Column projectroleactor not found - using business logic (DISABLED)
2025-06-17 14:10:55,475 - INFO -      Skipping fallback FK population for projectroleactor_id (dim_projectroleactor) - only real matches will be set.
2025-06-17 14:10:55,475 - INFO -    Populating jiraworkflows_id from jiraissue.jiraworkflows
2025-06-17 14:10:55,477 - INFO -      Column jiraworkflows not found - using business logic (DISABLED)
2025-06-17 14:10:55,477 - INFO -      Skipping fallback FK population for jiraworkflows_id (dim_jiraworkflows) - only real matches will be set.
2025-06-17 14:10:55,477 - INFO -    Populating workflowscheme_id from jiraissue.workflowscheme
2025-06-17 14:10:55,479 - INFO -      Column workflowscheme not found - using business logic (DISABLED)
2025-06-17 14:10:55,479 - INFO -      Skipping fallback FK population for workflowscheme_id (dim_workflowscheme) - only real matches will be set.
2025-06-17 14:10:55,479 - INFO -    Populating workflowschemeentity_id from jiraissue.workflowschemeentity
2025-06-17 14:10:55,482 - INFO -      Column workflowschemeentity not found - using business logic (DISABLED)
2025-06-17 14:10:55,482 - INFO -      Skipping fallback FK population for workflowschemeentity_id (dim_workflowschemeentity) - only real matches will be set.
2025-06-17 14:10:55,482 - INFO -    Populating fieldconfigscheme_id from jiraissue.fieldconfigscheme
2025-06-17 14:10:55,485 - INFO -      Column fieldconfigscheme not found - using business logic (DISABLED)
2025-06-17 14:10:55,486 - INFO -      Skipping fallback FK population for fieldconfigscheme_id (dim_fieldconfigscheme) - only real matches will be set.
2025-06-17 14:10:55,486 - INFO -    Populating fieldconfiguration_id from jiraissue.fieldconfiguration
2025-06-17 14:10:55,492 - INFO -      Column fieldconfiguration not found - using business logic (DISABLED)
2025-06-17 14:10:55,493 - INFO -      Skipping fallback FK population for fieldconfiguration_id (dim_fieldconfiguration) - only real matches will be set.
2025-06-17 14:10:55,493 - INFO -    Populating fieldscreen_id from jiraissue.fieldscreen
2025-06-17 14:10:55,495 - INFO -      Column fieldscreen not found - using business logic (DISABLED)
2025-06-17 14:10:55,495 - INFO -      Skipping fallback FK population for fieldscreen_id (dim_fieldscreen) - only real matches will be set.
2025-06-17 14:10:55,496 - INFO -    Populating fieldscreentab_id from jiraissue.fieldscreentab
2025-06-17 14:10:55,498 - INFO -      Column fieldscreentab not found - using business logic (DISABLED)
2025-06-17 14:10:55,498 - INFO -      Skipping fallback FK population for fieldscreentab_id (dim_fieldscreentab) - only real matches will be set.
2025-06-17 14:10:55,498 - INFO -    Populating permissionscheme_id from jiraissue.permissionscheme
2025-06-17 14:10:55,501 - INFO -      Column permissionscheme not found - using business logic (DISABLED)
2025-06-17 14:10:55,501 - INFO -      Skipping fallback FK population for permissionscheme_id (dim_permissionscheme) - only real matches will be set.
2025-06-17 14:10:55,501 - INFO -    Populating schemepermissions_id from jiraissue.schemepermissions
2025-06-17 14:10:55,504 - INFO -      Column schemepermissions not found - using business logic (DISABLED)
2025-06-17 14:10:55,504 - INFO -      Skipping fallback FK population for schemepermissions_id (dim_schemepermissions) - only real matches will be set.
2025-06-17 14:10:55,504 - INFO -    Populating projectrole_id from jiraissue.projectrole
2025-06-17 14:10:55,507 - INFO -      Column projectrole not found - using business logic (DISABLED)
2025-06-17 14:10:55,507 - INFO -      Skipping fallback FK population for projectrole_id (dim_projectrole) - only real matches will be set.
2025-06-17 14:10:55,507 - INFO -    Populating pluginversion_id from jiraissue.pluginversion
2025-06-17 14:10:55,509 - INFO -      Column pluginversion not found - using business logic (DISABLED)
2025-06-17 14:10:55,510 - INFO -      Skipping fallback FK population for pluginversion_id (dim_pluginversion) - only real matches will be set.
2025-06-17 14:10:55,510 - INFO -    Populating managedconfigurationitem_id from jiraissue.managedconfigurationitem
2025-06-17 14:10:55,512 - INFO -      Column managedconfigurationitem not found - using business logic (DISABLED)
2025-06-17 14:10:55,512 - INFO -      Skipping fallback FK population for managedconfigurationitem_id (dim_managedconfigurationitem) - only real matches will be set.
2025-06-17 14:10:55,512 - INFO -    Populating AO_60DB71_RAPIDVIEW_id from jiraissue.AO_60DB71_RAPIDVIEW
2025-06-17 14:10:55,515 - INFO -      Column AO_60DB71_RAPIDVIEW not found - using business logic (DISABLED)
2025-06-17 14:10:55,515 - INFO -      Skipping fallback FK population for AO_60DB71_RAPIDVIEW_id (dim_AO_60DB71_RAPIDVIEW) - only real matches will be set.
2025-06-17 14:10:55,515 - INFO -    Populating AO_C77861_AUDIT_ENTITY_id from jiraissue.AO_C77861_AUDIT_ENTITY
2025-06-17 14:10:55,517 - INFO -      Column AO_C77861_AUDIT_ENTITY not found - using business logic (DISABLED)
2025-06-17 14:10:55,517 - INFO -      Skipping fallback FK population for AO_C77861_AUDIT_ENTITY_id (dim_AO_C77861_AUDIT_ENTITY) - only real matches will be set.
2025-06-17 14:10:55,517 - INFO -    Populating AO_C77861_AUDIT_ACTION_CACHE_id from jiraissue.AO_C77861_AUDIT_ACTION_CACHE
2025-06-17 14:10:55,519 - INFO -      Column AO_C77861_AUDIT_ACTION_CACHE not found - using business logic (DISABLED)
2025-06-17 14:10:55,520 - INFO -      Skipping fallback FK population for AO_C77861_AUDIT_ACTION_CACHE_id (dim_AO_C77861_AUDIT_ACTION_CACHE) - only real matches will be set.
2025-06-17 14:10:55,520 - INFO -    Populating AO_C77861_AUDIT_CATEGORY_CACHE_id from jiraissue.AO_C77861_AUDIT_CATEGORY_CACHE
2025-06-17 14:10:55,522 - INFO -      Column AO_C77861_AUDIT_CATEGORY_CACHE not found - using business logic (DISABLED)
2025-06-17 14:10:55,522 - INFO -      Skipping fallback FK population for AO_C77861_AUDIT_CATEGORY_CACHE_id (dim_AO_C77861_AUDIT_CATEGORY_CACHE) - only real matches will be set.
2025-06-17 14:10:55,522 - INFO - 🎯 Foreign keys successfully populated: 4/33
2025-06-17 14:10:55,564 - INFO - 
📊 FK POPULATION SUMMARY:
2025-06-17 14:10:55,564 - INFO -    ❌ AO_60DB71_RAPIDVIEW_id: 0 populated
2025-06-17 14:10:55,564 - INFO -    ❌ AO_C77861_AUDIT_ACTION_CACHE_id: 0 populated
2025-06-17 14:10:55,564 - INFO -    ❌ AO_C77861_AUDIT_CATEGORY_CACHE_id: 0 populated
2025-06-17 14:10:55,564 - INFO -    ❌ AO_C77861_AUDIT_ENTITY_id: 0 populated
2025-06-17 14:10:55,564 - INFO -    ❌ component_id: 0 populated
2025-06-17 14:10:55,564 - INFO -    ❌ customfield_id: 0 populated
2025-06-17 14:10:55,564 - INFO -    ❌ customfieldvalue_id: 0 populated
2025-06-17 14:10:55,564 - INFO -    ❌ cwd_group_id: 0 populated
2025-06-17 14:10:55,564 - INFO -    ❌ cwd_membership_id: 0 populated
2025-06-17 14:10:55,565 - INFO -    ❌ cwd_user_id: 0 populated
2025-06-17 14:10:55,565 - INFO -    ❌ fieldconfigscheme_id: 0 populated
2025-06-17 14:10:55,565 - INFO -    ❌ fieldconfiguration_id: 0 populated
2025-06-17 14:10:55,565 - INFO -    ❌ fieldscreen_id: 0 populated
2025-06-17 14:10:55,565 - INFO -    ❌ fieldscreentab_id: 0 populated
2025-06-17 14:10:55,565 - INFO -    ❌ fileattachment_id: 0 populated
2025-06-17 14:10:55,565 - INFO -    ❌ issuelinktype_id: 0 populated
2025-06-17 14:10:55,565 - INFO -    ✅ issuestatus_id: 2,335 populated
2025-06-17 14:10:55,565 - INFO -    ❌ issuetype_id: 0 populated
2025-06-17 14:10:55,565 - INFO -    ❌ jiraworkflows_id: 0 populated
2025-06-17 14:10:55,565 - INFO -    ❌ label_id: 0 populated
2025-06-17 14:10:55,565 - INFO -    ❌ managedconfigurationitem_id: 0 populated
2025-06-17 14:10:55,565 - INFO -    ❌ permissionscheme_id: 0 populated
2025-06-17 14:10:55,565 - INFO -    ❌ pluginversion_id: 0 populated
2025-06-17 14:10:55,565 - INFO -    ✅ priority_id: 2,335 populated
2025-06-17 14:10:55,565 - INFO -    ✅ project_id: 2,335 populated
2025-06-17 14:10:55,566 - INFO -    ❌ projectrole_id: 0 populated
2025-06-17 14:10:55,566 - INFO -    ❌ projectroleactor_id: 0 populated
2025-06-17 14:10:55,566 - INFO -    ✅ resolution_id: 1,420 populated
2025-06-17 14:10:55,566 - INFO -    ❌ schemepermissions_id: 0 populated
2025-06-17 14:10:55,566 - INFO -    ❌ workflow_id: 0 populated
2025-06-17 14:10:55,566 - INFO -    ❌ workflowscheme_id: 0 populated
2025-06-17 14:10:55,566 - INFO -    ❌ workflowschemeentity_id: 0 populated
2025-06-17 14:10:55,566 - INFO -    ❌ worklog_id: 0 populated
2025-06-17 14:10:55,566 - INFO -    FK colonnes populated: 4
2025-06-17 14:10:55,566 - INFO - 
INSERTION STAR SCHEMA TERMINEE!
2025-06-17 14:10:55,566 - INFO - Duree: 2.26 secondes
2025-06-17 14:10:55,566 - INFO - Dimensions chargees: 30/31
2025-06-17 14:10:55,566 - INFO - Fact records: 2,335
2025-06-17 14:10:55,566 - INFO - Total enregistrements: 23,831
2025-06-17 14:10:55,566 - INFO - Erreurs: 0
2025-06-17 14:10:55,566 - INFO - 
STAR SCHEMA INSERTION REUSSIE!
2025-06-17 14:10:55,566 - INFO -    30 dimensions chargees
2025-06-17 14:10:55,566 - INFO -    2,335 fact records
2025-06-17 14:10:55,566 - INFO -    Architecture star schema complete
2025-06-17 14:10:55,566 - INFO - 
PROCHAINE ETAPE:
2025-06-17 14:10:55,567 - INFO -    Executer: python 03_load_validation.py
