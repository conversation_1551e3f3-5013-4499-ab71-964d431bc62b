#!/usr/bin/env python3
"""
Enhanced Load Validation Script
Validates star schema tables with detailed error reporting
"""

import logging
import psycopg2
import json
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# Configure logging
logger = logging.getLogger(__name__)

def configure_logging():
    """Configure logging with file and console output"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
        handlers=[
            logging.FileHandler('load_validation.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def load_config() -> Dict:
    """Load ETL configuration with robust error handling"""
    try:
        script_path = Path(__file__).absolute()
        deployment_dir = script_path.parent.parent
        config_path = deployment_dir / 'etl_config.json'
        
        logger.info(f"Loading config from: {config_path}")
        
        if not config_path.exists():
            raise FileNotFoundError(f"Config file not found at: {config_path}")
            
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        # Validate required config sections
        required_sections = ['database', 'schemas']
        for section in required_sections:
            if section not in config:
                raise ValueError(f"Missing required config section: {section}")
                
        return config
    except Exception as e:
        logger.error(f"Config load failed: {str(e)}", exc_info=True)
        raise

class SchemaValidator:
    def __init__(self, config: Dict):
        self.config = config
        self.db_config = config['database']
        self.schema = config['schemas']['load']
        self.validation_results = {
            'start_time': datetime.now().isoformat(),
            'checks': [],
            'error_count': 0,
            'warning_count': 0
        }

    def _log_validation_result(self, check_name: str, success: bool, 
                             message: str = "", details: Dict = None):
        """Record validation results with details"""
        result = {
            'check': check_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat()
        }
        
        if details:
            result['details'] = details
            
        self.validation_results['checks'].append(result)
        
        if not success:
            self.validation_results['error_count'] += 1
            logger.error(f"VALIDATION FAILED: {check_name} - {message}")
        else:
            logger.info(f"VALIDATION PASSED: {check_name}")

    def _execute_sql(self, cursor, sql: str, params: Tuple = None) -> List:
        """Execute SQL with error handling"""
        try:
            cursor.execute(sql, params or ())
            return cursor.fetchall()
        except Exception as e:
            self._log_validation_result(
                "SQL Execution",
                False,
                f"Failed to execute SQL query",
                {
                    'sql': sql,
                    'error': str(e),
                    'params': params
                }
            )
            raise

    def validate_table_existence(self, cursor) -> bool:
        """Check if all required tables exist"""
        expected_tables = [
            'dim_date', 'dim_cwd_user', 'dim_cwd_group', 'dim_project',
            'dim_component', 'dim_issuetype', 'dim_priority', 'dim_resolution',
            'dim_issuestatus', 'dim_customfield', 'dim_label',
            'dim_ao_60db71_rapidview', 'dim_ao_c77861_audit_entity',
            'fact_jiraissue', 'fact_worklog', 'fact_customfieldvalue',
            'fact_changelog', 'fact_sprint_issue'
        ]
        
        missing_tables = []
        existing_tables = []
        
        for table in expected_tables:
            try:
                self._execute_sql(
                    cursor,
                    "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = %s AND table_name = %s)",
                    (self.schema, table)
                )
                exists = cursor.fetchone()[0]
                
                if not exists:
                    missing_tables.append(table)
                else:
                    existing_tables.append(table)
                    
            except Exception as e:
                self._log_validation_result(
                    f"Table Existence Check - {table}",
                    False,
                    f"Error checking table existence",
                    {'error': str(e)}
                )
                continue
                
        if missing_tables:
            self._log_validation_result(
                "Table Existence",
                False,
                f"Missing {len(missing_tables)} tables",
                {
                    'missing_tables': missing_tables,
                    'existing_tables': existing_tables
                }
            )
            return False
            
        self._log_validation_result(
            "Table Existence",
            True,
            f"All {len(expected_tables)} tables exist"
        )
        return True

    def validate_foreign_keys(self, cursor) -> bool:
        """Validate all foreign key relationships"""
        # Define expected FK relationships
        fk_checks = [
            {
                'table': 'fact_jiraissue',
                'column': 'project_id',
                'ref_table': 'dim_project',
                'ref_column': 'project_id'
            },
            {
                'table': 'fact_jiraissue',
                'column': 'reporter_id',
                'ref_table': 'dim_cwd_user',
                'ref_column': 'user_id'
            },
            # Add all other FK relationships here...
        ]
        
        failed_checks = []
        passed_checks = []
        
        for check in fk_checks:
            try:
                # Check for orphaned FK references
                sql = f"""
                    SELECT COUNT(*) 
                    FROM {self.schema}.{check['table']} f
                    LEFT JOIN {self.schema}.{check['ref_table']} d
                        ON f.{check['column']} = d.{check['ref_column']}
                    WHERE f.{check['column']} IS NOT NULL
                    AND d.{check['ref_column']} IS NULL
                """
                result = self._execute_sql(cursor, sql)
                orphaned_count = result[0][0]
                
                if orphaned_count > 0:
                    failed_checks.append({
                        **check,
                        'orphaned_records': orphaned_count
                    })
                else:
                    passed_checks.append(check)
                    
            except Exception as e:
                self._log_validation_result(
                    f"FK Check - {check['table']}.{check['column']}",
                    False,
                    f"Error checking FK relationship",
                    {
                        'error': str(e),
                        'check': check
                    }
                )
                continue
                
        if failed_checks:
            self._log_validation_result(
                "Foreign Key Validation",
                False,
                f"{len(failed_checks)} FK violations found",
                {
                    'failed_checks': failed_checks,
                    'passed_checks': passed_checks
                }
            )
            return False
            
        self._log_validation_result(
            "Foreign Key Validation",
            True,
            f"All {len(fk_checks)} FK relationships valid"
        )
        return True

    def validate_data_quality(self, cursor) -> bool:
        """Perform data quality checks"""
        checks = [
            {
                'name': 'Null PK Check',
                'sql': "SELECT table_name FROM information_schema.columns WHERE table_schema = %s AND column_name = 'id' AND is_nullable = 'YES'",
                'params': (self.schema,),
                'expect_empty': True
            },
            {
                'name': 'Fact JiraIssue Data',
                'sql': "SELECT COUNT(*) FROM load.fact_jiraissue",
                'expect_min': 1
            },
            # Add more data quality checks as needed
        ]
        
        failed_checks = []
        
        for check in checks:
            try:
                result = self._execute_sql(cursor, check['sql'], check.get('params'))
                
                if 'expect_empty' in check and check['expect_empty']:
                    if result:
                        failed_checks.append({
                            'check': check['name'],
                            'result': result,
                            'expected': 'Empty result',
                            'actual': f"Found {len(result)} rows"
                        })
                elif 'expect_min' in check:
                    if result[0][0] < check['expect_min']:
                        failed_checks.append({
                            'check': check['name'],
                            'result': result[0][0],
                            'expected': f"At least {check['expect_min']}",
                            'actual': result[0][0]
                        })
                        
            except Exception as e:
                self._log_validation_result(
                    check['name'],
                    False,
                    f"Data quality check failed",
                    {
                        'error': str(e),
                        'check': check
                    }
                )
                continue
                
        if failed_checks:
            self._log_validation_result(
                "Data Quality",
                False,
                f"{len(failed_checks)} data quality issues found",
                {'failed_checks': failed_checks}
            )
            return False
            
        self._log_validation_result(
            "Data Quality",
            True,
            "All data quality checks passed"
        )
        return True

    def validate_schema(self) -> Tuple[bool, str]:
        """Run all validation checks"""
        conn = None
        try:
            conn = psycopg2.connect(
                host=self.db_config['host'],
                port=self.db_config.get('port', 5432),
                database=self.db_config['target_db'],
                user=self.db_config['user'],
                password=self.db_config['password']
            )
            cursor = conn.cursor()

            # Run all validation checks
            checks = [
                ('Table Existence', self.validate_table_existence(cursor)),
                ('Foreign Keys', self.validate_foreign_keys(cursor)),
                ('Data Quality', self.validate_data_quality(cursor))
            ]

            # Determine overall status
            overall_success = all(success for (_, success) in checks)
            message = "All checks passed" if overall_success else "Validation failed"
            
            self.validation_results['end_time'] = datetime.now().isoformat()
            self.validation_results['success'] = overall_success
            self.validation_results['message'] = message
            
            return overall_success, message

        except Exception as e:
            self._log_validation_result(
                "Validation Process",
                False,
                "Fatal error during validation",
                {'error': str(e)}
            )
            return False, str(e)
        finally:
            if conn:
                conn.close()

def main() -> int:
    """Main execution with comprehensive error handling"""
    try:
        configure_logging()
        logger.info("=== Starting Enhanced Load Validation ===")
        
        config = load_config()
        validator = SchemaValidator(config)
        
        success, message = validator.validate_schema()
        
        # Save detailed results
        with open('load_validation.json', 'w', encoding='utf-8') as f:
            json.dump(validator.validation_results, f, indent=2, default=str)
            
        logger.info(f"Validation completed: {'SUCCESS' if success else 'FAILED'} - {message}")
        return 0 if success else 1
        
    except Exception as e:
        logger.critical(f"Fatal error in validation process: {str(e)}", exc_info=True)
        return 1

if __name__ == "__main__":
    sys.exit(main())