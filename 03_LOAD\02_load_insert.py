#!/usr/bin/env python3
"""
FINAL LOAD INSERT - DATA LOADING
Loads data into constellation schema with:
- Strict table order enforcement
- Detailed error logging
- Batch processing
- FK validation
"""

import psycopg2
import logging
from datetime import datetime
import sys
import os
import time
from psycopg2.extras import execute_batch

# Add utilities directory to path
utilities_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '05_UTILITIES')
sys.path.append(utilities_path)
from config_manager import ETLConfigManager

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('load_insert.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class LoadDataProcessor:
    def __init__(self):
        self.stats = {
            'start_time': datetime.now(),
            'tables_processed': 0,
            'total_records': 0,
            'errors': []
        }
        self.table_order = [
            # Dimensions first
            'dim_date', 'dim_cwd_user', 'dim_cwd_group', 'dim_project',
            'dim_component', 'dim_issuetype', 'dim_priority', 'dim_resolution',
            'dim_issuestatus', 'dim_customfield', 'dim_label',
            'dim_ao_60db71_rapidview', 'dim_ao_c77861_audit_entity',
            'dim_ao_c77861_audit_action_cache', 'dim_ao_c77861_audit_category_cache',
            'dim_jiraworkflows', 'dim_workflowscheme', 'dim_fieldconfigscheme',
            'dim_fieldconfiguration', 'dim_fieldscreen', 'dim_fieldscreentab',
            'dim_permissionscheme', 'dim_schemepermissions', 'dim_pluginversion',
            'dim_managedconfigurationitem', 'dim_projectrole', 'dim_projectroleactor',
            'dim_cwd_membership',
            
            # Facts last
            'fact_jiraissue', 'fact_worklog', 'fact_customfieldvalue',
            'fact_changelog', 'fact_sprint_issue'
        ]
        self.table_mappings = {
            'dim_date': None,  # Special handling
            'dim_cwd_user': 'cwd_user_clean',
            'dim_cwd_group': 'cwd_group_clean',
            'dim_project': 'project_clean',
            'dim_component': 'component_clean',
            'dim_issuetype': 'issuetype_clean',
            'dim_priority': 'priority_clean',
            'dim_resolution': 'resolution_clean',
            'dim_issuestatus': 'issuestatus_clean',
            'dim_customfield': 'customfield_clean',
            'dim_label': 'label_clean',
            'dim_ao_60db71_rapidview': 'AO_60DB71_RAPIDVIEW_clean',
            'dim_ao_c77861_audit_entity': 'AO_C77861_AUDIT_ENTITY_clean',
            'dim_ao_c77861_audit_action_cache': 'AO_C77861_AUDIT_ACTION_CACHE_clean',
            'dim_ao_c77861_audit_category_cache': 'AO_C77861_AUDIT_CATEGORY_CACHE_clean',
            'dim_jiraworkflows': 'jiraworkflows_clean',
            'dim_workflowscheme': 'workflowscheme_clean',
            'dim_fieldconfigscheme': 'fieldconfigscheme_clean',
            'dim_fieldconfiguration': 'fieldconfiguration_clean',
            'dim_fieldscreen': 'fieldscreen_clean',
            'dim_fieldscreentab': 'fieldscreentab_clean',
            'dim_permissionscheme': 'permissionscheme_clean',
            'dim_schemepermissions': 'schemepermissions_clean',
            'dim_pluginversion': 'pluginversion_clean',
            'dim_managedconfigurationitem': 'managedconfigurationitem_clean',
            'dim_projectrole': 'projectrole_clean',
            'dim_projectroleactor': 'projectroleactor_clean',
            'dim_cwd_membership': 'cwd_membership_clean',
            'fact_jiraissue': 'jiraissue_clean',
            'fact_worklog': 'worklog_clean',
            'fact_customfieldvalue': 'customfieldvalue_clean',
            'fact_changelog': 'AO_C77861_AUDIT_ENTITY_clean',
            'fact_sprint_issue': 'sprint_issue_mapping'  # Special handling
        }

    def connect_dw(self):
        try:
            parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            config_file = os.path.join(parent_dir, 'etl_config.json')
            config_manager = ETLConfigManager(config_file)
            db_config = config_manager.config["database"]
            dw_config = {
                'host': db_config['host'],
                'port': db_config['port'],
                'database': db_config['target_db'],
                'user': db_config['user'],
                'password': db_config['password']
            }
            return psycopg2.connect(**dw_config)
        except Exception as e:
            logger.error(f"DW connection error: {e}")
            raise

    def load_dim_date(self):
        """Special handling for date dimension"""
        conn = self.connect_dw()
        try:
            with conn.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO load.dim_date
                    SELECT 
                        datum AS date_id,
                        EXTRACT(YEAR FROM datum)::INTEGER AS year,
                        EXTRACT(QUARTER FROM datum)::INTEGER AS quarter,
                        EXTRACT(MONTH FROM datum)::INTEGER AS month,
                        EXTRACT(DAY FROM datum)::INTEGER AS day,
                        EXTRACT(WEEK FROM datum)::INTEGER AS week,
                        EXTRACT(DOW FROM datum)::INTEGER AS day_of_week,
                        TO_CHAR(datum, 'Day') AS day_name,
                        TO_CHAR(datum, 'Month') AS month_name,
                        EXTRACT(DOW FROM datum) IN (0, 6) AS is_weekend
                    FROM generate_series(
                        CURRENT_DATE - INTERVAL '5 years',
                        CURRENT_DATE + INTERVAL '2 years',
                        INTERVAL '1 day'
                    ) AS datum
                """)
                count = cursor.rowcount
                conn.commit()
                self.stats['tables_processed'] += 1
                self.stats['total_records'] += count
                logger.info(f"Loaded dim_date with {count} records")
        except Exception as e:
            logger.error(f"Error loading dim_date: {e}")
            conn.rollback()
            self.stats['errors'].append(f"dim_date: {str(e)}")

    def load_table_data(self, table_name):
        if table_name == 'dim_date':
            return self.load_dim_date()

        transform_table = self.table_mappings.get(table_name)
        if not transform_table:
            logger.warning(f"No transform mapping for {table_name}")
            return 0

        conn = self.connect_dw()
        try:
            with conn.cursor() as cursor:
                logger.info(f"Loading {table_name} from transform.{transform_table}")

                # Get column mappings
                cursor.execute(f"""
                    SELECT 
                        t.column_name AS transform_col,
                        l.column_name AS load_col
                    FROM information_schema.columns t
                    JOIN information_schema.columns l 
                        ON t.column_name = l.column_name
                    WHERE 
                        t.table_schema = 'transform' AND t.table_name = %s AND
                        l.table_schema = 'load' AND l.table_name = %s
                    ORDER BY t.ordinal_position
                """, (transform_table, table_name))
                column_mappings = cursor.fetchall()

                if not column_mappings:
                    logger.warning(f"No common columns for {table_name}")
                    return 0

                # Build SELECT and INSERT statements
                select_cols = []
                insert_cols = []
                for mapping in column_mappings:
                    select_cols.append(f'"{mapping[0]}"')
                    insert_cols.append(f'"{mapping[1]}"')

                # Special handling for AO tables with uppercase columns
                if table_name.startswith('dim_ao_'):
                    select_cols = [f'"{col.upper()}"' for col in select_cols]

                # Handle special cases
                if table_name == 'fact_jiraissue':
                    select_cols.extend(['"summary"', 'NULL::numeric(18,2) AS story_points'])
                    insert_cols.extend(['summary', 'story_points'])

                select_sql = f"""
                    SELECT {', '.join(select_cols)}
                    FROM transform."{transform_table}"
                    WHERE instance_id = 1
                """

                insert_sql = f"""
                    INSERT INTO load."{table_name}"
                    ({', '.join(insert_cols)})
                    VALUES ({', '.join(['%s'] * len(insert_cols))})
                """

                # Execute and load in batches
                cursor.execute(select_sql)
                batch_size = 1000 if table_name.startswith('dim_') else 500
                total_records = 0

                while True:
                    batch = cursor.fetchmany(batch_size)
                    if not batch:
                        break

                    try:
                        execute_batch(cursor, insert_sql, batch)
                        total_records += len(batch)
                    except psycopg2.Error as e:
                        logger.error(f"Batch insert failed for {table_name}: {e}")
                        conn.rollback()
                        # Fallback to single-record inserts
                        for record in batch:
                            try:
                                cursor.execute(insert_sql, record)
                            except Exception as e:
                                self.stats['errors'].append(f"{table_name}: {str(e)}")
                                continue
                        total_records += len(batch) - self.stats['errors'].count(table_name)

                # Post-load updates for fact_jiraissue
                if table_name == 'fact_jiraissue':
                    cursor.execute("""
                        UPDATE load.fact_jiraissue fi
                        SET story_points = cfv.numbervalue
                        FROM transform.customfieldvalue_clean cfv
                        JOIN transform.customfield_clean cf ON cfv.customfield = cf.id
                        WHERE fi.issue_id = cfv.issue AND cf.cfname ILIKE '%Story Points%'
                    """)

                conn.commit()
                self.stats['tables_processed'] += 1
                self.stats['total_records'] += total_records
                logger.info(f"Loaded {total_records} records into {table_name}")
                return total_records

        except Exception as e:
            logger.error(f"Error loading {table_name}: {e}")
            conn.rollback()
            self.stats['errors'].append(f"{table_name}: {str(e)}")
            return 0
        finally:
            conn.close()

    def process_all_tables(self):
        logger.info("Starting data load process")
        for table_name in self.table_order:
            logger.info(f"Processing {table_name}...")
            start_time = time.time()
            records_loaded = self.load_table_data(table_name)
            duration = time.time() - start_time
            logger.info(f"Completed {table_name} in {duration:.2f}s ({records_loaded} records)")

    def verify_load(self):
        conn = self.connect_dw()
        try:
            with conn.cursor() as cursor:
                cursor.execute("""
                    SELECT table_name, 
                           (xpath('/row/cnt/text()', query_to_xml(format('SELECT COUNT(*) AS cnt FROM load.%I', table_name), false, true, '')))[1]::text::int
                    FROM information_schema.tables
                    WHERE table_schema = 'load'
                    ORDER BY table_name
                """)
                results = cursor.fetchall()
                logger.info("\nLoad Verification Results:")
                for table, count in results:
                    logger.info(f"{table}: {count} records")
        except Exception as e:
            logger.error(f"Verification error: {e}")
        finally:
            conn.close()

if __name__ == "__main__":
    logger.info("Starting Load Data Process")
    try:
        processor = LoadDataProcessor()
        processor.process_all_tables()
        processor.verify_load()
        
        duration = (datetime.now() - processor.stats['start_time']).total_seconds()
        logger.info(f"\nLoad completed in {duration:.2f} seconds")
        logger.info(f"Tables processed: {processor.stats['tables_processed']}")
        logger.info(f"Total records loaded: {processor.stats['total_records']}")
        
        if processor.stats['errors']:
            logger.error(f"\nErrors encountered ({len(processor.stats['errors'])}°):")
            for error in processor.stats['errors'][:10]:  # Show first 10 errors
                logger.error(f"  - {error}")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)