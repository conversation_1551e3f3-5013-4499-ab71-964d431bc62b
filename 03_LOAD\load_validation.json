{"start_time": "2025-06-19T13:35:43.120858", "checks": [{"check": "Table Existence Check - dim_date", "success": false, "message": "Error checking table existence", "timestamp": "2025-06-19T13:35:43.152982", "details": {"error": "'NoneType' object is not subscriptable"}}, {"check": "Table Existence Check - dim_cwd_user", "success": false, "message": "Error checking table existence", "timestamp": "2025-06-19T13:35:43.154783", "details": {"error": "'NoneType' object is not subscriptable"}}, {"check": "Table Existence Check - dim_cwd_group", "success": false, "message": "Error checking table existence", "timestamp": "2025-06-19T13:35:43.156674", "details": {"error": "'NoneType' object is not subscriptable"}}, {"check": "Table Existence Check - dim_project", "success": false, "message": "Error checking table existence", "timestamp": "2025-06-19T13:35:43.157878", "details": {"error": "'NoneType' object is not subscriptable"}}, {"check": "Table Existence Check - dim_component", "success": false, "message": "Error checking table existence", "timestamp": "2025-06-19T13:35:43.159084", "details": {"error": "'NoneType' object is not subscriptable"}}, {"check": "Table Existence Check - dim_issuetype", "success": false, "message": "Error checking table existence", "timestamp": "2025-06-19T13:35:43.160345", "details": {"error": "'NoneType' object is not subscriptable"}}, {"check": "Table Existence Check - dim_priority", "success": false, "message": "Error checking table existence", "timestamp": "2025-06-19T13:35:43.161578", "details": {"error": "'NoneType' object is not subscriptable"}}, {"check": "Table Existence Check - dim_resolution", "success": false, "message": "Error checking table existence", "timestamp": "2025-06-19T13:35:43.162752", "details": {"error": "'NoneType' object is not subscriptable"}}, {"check": "Table Existence Check - dim_issuestatus", "success": false, "message": "Error checking table existence", "timestamp": "2025-06-19T13:35:43.164590", "details": {"error": "'NoneType' object is not subscriptable"}}, {"check": "Table Existence Check - dim_customfield", "success": false, "message": "Error checking table existence", "timestamp": "2025-06-19T13:35:43.165826", "details": {"error": "'NoneType' object is not subscriptable"}}, {"check": "Table Existence Check - dim_label", "success": false, "message": "Error checking table existence", "timestamp": "2025-06-19T13:35:43.167044", "details": {"error": "'NoneType' object is not subscriptable"}}, {"check": "Table Existence Check - dim_ao_60db71_rapidview", "success": false, "message": "Error checking table existence", "timestamp": "2025-06-19T13:35:43.168260", "details": {"error": "'NoneType' object is not subscriptable"}}, {"check": "Table Existence Check - dim_ao_c77861_audit_entity", "success": false, "message": "Error checking table existence", "timestamp": "2025-06-19T13:35:43.168899", "details": {"error": "'NoneType' object is not subscriptable"}}, {"check": "Table Existence Check - fact_jiraissue", "success": false, "message": "Error checking table existence", "timestamp": "2025-06-19T13:35:43.170102", "details": {"error": "'NoneType' object is not subscriptable"}}, {"check": "Table Existence Check - fact_worklog", "success": false, "message": "Error checking table existence", "timestamp": "2025-06-19T13:35:43.171347", "details": {"error": "'NoneType' object is not subscriptable"}}, {"check": "Table Existence Check - fact_customfieldvalue", "success": false, "message": "Error checking table existence", "timestamp": "2025-06-19T13:35:43.172560", "details": {"error": "'NoneType' object is not subscriptable"}}, {"check": "Table Existence Check - fact_changelog", "success": false, "message": "Error checking table existence", "timestamp": "2025-06-19T13:35:43.173810", "details": {"error": "'NoneType' object is not subscriptable"}}, {"check": "Table Existence Check - fact_sprint_issue", "success": false, "message": "Error checking table existence", "timestamp": "2025-06-19T13:35:43.174861", "details": {"error": "'NoneType' object is not subscriptable"}}, {"check": "Table Existence", "success": true, "message": "All 18 tables exist", "timestamp": "2025-06-19T13:35:43.174861"}, {"check": "Foreign Key Validation", "success": true, "message": "All 2 FK relationships valid", "timestamp": "2025-06-19T13:35:43.176779"}, {"check": "Data Quality", "success": false, "message": "1 data quality issues found", "timestamp": "2025-06-19T13:35:43.181228", "details": {"failed_checks": [{"check": "Fact JiraIssue Data", "result": 0, "expected": "At least 1", "actual": 0}]}}], "error_count": 19, "warning_count": 0, "end_time": "2025-06-19T13:35:43.181228", "success": false, "message": "Validation failed"}