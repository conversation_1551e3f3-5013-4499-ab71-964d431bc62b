#!/usr/bin/env python3
"""
UNIVERSAL DATA WAREHOUSE LOADER
5-Layer Pipeline for all 33 Jira analytics tables:
1. Raw Mirror: Clone transform schema to load_raw
2. Safe Load: Bulk load data with error logging
3. Schema Surgery: Build star schema with fact/dim tables
4. Analyst Transformation: Apply business logic (e.g., story points)
5. Seman<PERSON> Layer: Create analyst-friendly views
"""

import psycopg2
import logging
import pandas as pd
from datetime import datetime
import sys
import os
from sqlalchemy import create_engine
import re

# Add utilities directory to path
utilities_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '05_UTILITIES')
sys.path.append(utilities_path)
from config_manager import ETLConfigManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('universal_loader.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class UniversalLoader:
    def __init__(self):
        self.stats = {
            'start_time': datetime.now(),
            'tables_processed': 0,
            'total_records': 0,
            'errors': []
        }
        self.conn = self.connect_dw()
        self.engine = self.connect_engine()
        self.transform_tables = self.get_transform_tables()
        self.user_columns = ['reporter', 'assignee', 'creator', 'lead', 'author', 'owner_user_name']
        self.customfield_columns = ['item_id', 'fieldid']
        self.all_critical_tables = [
            'jiraissue', 'project', 'component', 'customfield', 'customfieldvalue',
            'worklog', 'fileattachment', 'issuelinktype', 'label', 'cwd_group',
            'cwd_user', 'cwd_membership', 'projectroleactor', 'jiraworkflows',
            'workflowscheme', 'workflowschemeentity', 'fieldconfigscheme',
            'fieldconfiguration', 'fieldscreen', 'fieldscreentab', 'permissionscheme',
            'schemepermissions', 'priority', 'issuestatus', 'resolution', 'issuetype',
            'projectrole', 'pluginversion', 'managedconfigurationitem',
            'AO_60DB71_RAPIDVIEW', 'AO_C77861_AUDIT_ENTITY',
            'AO_C77861_AUDIT_ACTION_CACHE', 'AO_C77861_AUDIT_CATEGORY_CACHE'
        ]

    def connect_dw(self):
        """Connect to data warehouse"""
        try:
            parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            config = ETLConfigManager(os.path.join(parent_dir, 'etl_config.json')).config
            db = config["database"]
            return psycopg2.connect(
                host=db['host'],
                port=db['port'],
                database=db['target_db'],
                user=db['user'],
                password=db['password']
            )
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            raise

    def connect_engine(self):
        """Create SQLAlchemy engine"""
        try:
            parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            config = ETLConfigManager(os.path.join(parent_dir, 'etl_config.json')).config
            db = config["database"]
            return create_engine(f"postgresql://{db['user']}:{db['password']}@{db['host']}:{db['port']}/{db['target_db']}")
        except Exception as e:
            logger.error(f"Engine creation failed: {e}")
            raise

    def get_transform_tables(self):
        """Get all transform tables"""
        with self.conn.cursor() as cur:
            cur.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'transform'
                AND table_name LIKE '%_clean'
            """)
            return [row[0].replace('_clean', '') for row in cur.fetchall()]

    def layer_1_raw_mirror(self):
        """Layer 1: Clone transform schema to load_raw"""
        with self.conn.cursor() as cur:
            cur.execute("CREATE SCHEMA IF NOT EXISTS load_raw")
            for table in self.transform_tables:
                try:
                    clean_table = f"{table}_clean"
                    raw_table = table
                    cur.execute(f"""
                        DROP TABLE IF EXISTS load_raw.{raw_table};
                        CREATE TABLE load_raw.{raw_table} 
                        (LIKE transform."{clean_table}" INCLUDING DEFAULTS)
                    """)
                    logger.info(f"Cloned transform.{clean_table} to load_raw.{raw_table}")
                except Exception as e:
                    logger.error(f"Failed to clone {table}: {e}")
                    self.stats['errors'].append(f"Clone {table}: {str(e)}")
            self.conn.commit()

    def layer_2_safe_load(self):
        """Layer 2: Bulk load data with error logging"""
        with self.conn.cursor() as cur:
            cur.execute("""
                CREATE TABLE IF NOT EXISTS load_raw.error_log (
                    id SERIAL PRIMARY KEY,
                    table_name TEXT,
                    status TEXT,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            for table in self.transform_tables:
                try:
                    clean_table = f"{table}_clean"
                    raw_table = table
                    cur.execute(f"TRUNCATE load_raw.{raw_table}")
                    cur.execute(f"""
                        INSERT INTO load_raw.{raw_table}
                        SELECT * FROM transform."{clean_table}"
                        WHERE instance_id = 1
                    """)
                    logger.info(f"Loaded {raw_table}: {cur.rowcount:,} records")
                    self.stats['total_records'] += cur.rowcount
                    self.stats['tables_processed'] += 1
                except Exception as e:
                    logger.warning(f"Error loading {raw_table}: {str(e)}")
                    cur.execute(f"""
                        INSERT INTO load_raw.error_log (table_name, status, error_message)
                        VALUES (%s, %s, %s)
                    """, (raw_table, 'failed', str(e)))
                    self.stats['errors'].append(f"Load {raw_table}: {str(e)}")
            self.conn.commit()

    def layer_3_schema_surgery(self):
        """Layer 3: Create star schema with fact/dim tables"""
        with self.conn.cursor() as cur:
            cur.execute("CREATE SCHEMA IF NOT EXISTS load")
            fact_tables = ['jiraissue', 'worklog', 'customfieldvalue', 'fileattachment']
            dim_tables = [t for t in self.all_critical_tables if t not in fact_tables]
            
            # Create fact tables
            fact_ddls = {
                'jiraissue': """
                    CREATE TABLE load.fact_jiraissue (
                        issue_id BIGINT PRIMARY KEY,
                        project_id BIGINT,
                        reporter_id BIGINT,
                        assignee_id BIGINT,
                        status_id BIGINT,
                        created_date_id INTEGER,
                        resolution_date_id INTEGER,
                        story_points NUMERIC(18,2),
                        time_spent BIGINT,
                        summary TEXT,
                        instance_id INTEGER,
                        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    );
                    INSERT INTO load.fact_jiraissue (issue_id, project_id, reporter_id, assignee_id, status_id, created_date_id, resolution_date_id, story_points, time_spent, summary, instance_id)
                    SELECT 
                        id::BIGINT,
                        project::BIGINT,
                        reporter::BIGINT,
                        assignee::BIGINT,
                        issuestatus::BIGINT,
                        EXTRACT(EPOCH FROM created::DATE)::INTEGER,
                        EXTRACT(EPOCH FROM resolutiondate::DATE)::INTEGER,
                        NULL::NUMERIC(18,2),
                        timespent::BIGINT,
                        summary,
                        instance_id
                    FROM load_raw.jiraissue
                    WHERE id IS NOT NULL
                """,
                'worklog': """
                    CREATE TABLE load.fact_worklog (
                        worklog_id BIGINT PRIMARY KEY,
                        issue_id BIGINT,
                        author_id BIGINT,
                        worklog_date_id INTEGER,
                        time_worked BIGINT,
                        instance_id INTEGER,
                        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    );
                    INSERT INTO load.fact_worklog (worklog_id, issue_id, author_id, worklog_date_id, time_worked, instance_id)
                    SELECT 
                        id::BIGINT,
                        issueid::BIGINT,
                        author::BIGINT,
                        EXTRACT(EPOCH FROM created::DATE)::INTEGER,
                        timeworked::BIGINT,
                        instance_id
                    FROM load_raw.worklog
                    WHERE id IS NOT NULL
                """,
                'customfieldvalue': """
                    CREATE TABLE load.fact_customfieldvalue (
                        customfieldvalue_id BIGINT PRIMARY KEY,
                        issue_id BIGINT,
                        customfield_id BIGINT,
                        number_value NUMERIC,
                        text_value TEXT,
                        date_value DATE,
                        instance_id INTEGER,
                        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    );
                    INSERT INTO load.fact_customfieldvalue (customfieldvalue_id, issue_id, customfield_id, number_value, text_value, date_value, instance_id)
                    SELECT 
                        id::BIGINT,
                        issue::BIGINT,
                        customfield::BIGINT,
                        number_value,
                        COALESCE(text_value, string_value),
                        date_value::DATE,
                        instance_id
                    FROM load_raw.customfieldvalue
                    WHERE id IS NOT NULL
                """,
                'fileattachment': """
                    CREATE TABLE load.fact_fileattachment (
                        attachment_id BIGINT PRIMARY KEY,
                        issue_id BIGINT,
                        author_id BIGINT,
                        created_date_id INTEGER,
                        filesize BIGINT,
                        filename TEXT,
                        instance_id INTEGER,
                        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    );
                    INSERT INTO load.fact_fileattachment (attachment_id, issue_id, author_id, created_date_id, filesize, filename, instance_id)
                    SELECT 
                        id::BIGINT,
                        issueid::BIGINT,
                        author::BIGINT,
                        EXTRACT(EPOCH FROM created::DATE)::INTEGER,
                        filesize::BIGINT,
                        filename,
                        instance_id
                    FROM load_raw.fileattachment
                    WHERE id IS NOT NULL
                """
            }

            # Create dimension tables
            dim_ddls = {
                'project': """
                    CREATE TABLE load.dim_project (
                        project_id BIGINT PRIMARY KEY,
                        project_name TEXT,
                        project_key TEXT,
                        instance_id INTEGER,
                        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    );
                    INSERT INTO load.dim_project (project_id, project_name, project_key, instance_id)
                    SELECT DISTINCT
                        id::BIGINT,
                        pname,
                        pkey,
                        instance_id
                    FROM load_raw.project
                    WHERE id IS NOT NULL
                """,
                'cwd_user': """
                    CREATE TABLE load.dim_cwd_user (
                        user_id BIGINT PRIMARY KEY,
                        user_name TEXT,
                        email_address TEXT,
                        display_name TEXT,
                        instance_id INTEGER,
                        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    );
                    INSERT INTO load.dim_cwd_user (user_id, user_name, email_address, display_name, instance_id)
                    SELECT DISTINCT
                        id::BIGINT,
                        user_name,
                        email_address,
                        display_name,
                        instance_id
                    FROM load_raw.cwd_user
                    WHERE id IS NOT NULL
                """,
                'AO_60DB71_RAPIDVIEW': """
                    CREATE TABLE load.dim_ao_60db71_rapidview (
                        rapidview_id BIGINT PRIMARY KEY,
                        view_name TEXT,
                        owner TEXT,
                        instance_id INTEGER,
                        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    );
                    INSERT INTO load.dim_ao_60db71_rapidview (rapidview_id, view_name, owner, instance_id)
                    SELECT 
                        "ID"::BIGINT,
                        "NAME",
                        "OWNER_USER_NAME",
                        instance_id
                    FROM load_raw.AO_60DB71_RAPIDVIEW
                    WHERE "ID" IS NOT NULL
                """,
                'AO_C77861_AUDIT_ENTITY': """
                    CREATE TABLE load.dim_ao_c77861_audit_entity (
                        audit_entity_id BIGINT PRIMARY KEY,
                        user_id BIGINT,
                        action TEXT,
                        primary_resource_id BIGINT,
                        secondary_resource_id BIGINT,
                        instance_id INTEGER,
                        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    );
                    INSERT INTO load.dim_ao_c77861_audit_entity (audit_entity_id, user_id, action, primary_resource_id, secondary_resource_id, instance_id)
                    SELECT 
                        "ID"::BIGINT,
                        "USER_ID"::BIGINT,
                        "ACTION",
                        "PRIMARY_RESOURCE_ID"::BIGINT,
                        "SECONDARY_RESOURCE_ID"::BIGINT,
                        instance_id
                    FROM load_raw.AO_C77861_AUDIT_ENTITY
                    WHERE "ID" IS NOT NULL
                """,
                'AO_C77861_AUDIT_ACTION_CACHE': """
                    CREATE TABLE load.dim_ao_c77861_audit_action_cache (
                        action_id BIGINT PRIMARY KEY,
                        action TEXT,
                        instance_id INTEGER,
                        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    );
                    INSERT INTO load.dim_ao_c77861_audit_action_cache (action_id, action, instance_id)
                    SELECT 
                        "ID"::BIGINT,
                        "ACTION",
                        instance_id
                    FROM load_raw.AO_C77861_AUDIT_ACTION_CACHE
                    WHERE "ID" IS NOT NULL
                """,
                'AO_C77861_AUDIT_CATEGORY_CACHE': """
                    CREATE TABLE load.dim_ao_c77861_audit_category_cache (
                        category_id BIGINT PRIMARY KEY,
                        category TEXT,
                        instance_id INTEGER,
                        loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    );
                    INSERT INTO load.dim_ao_c77861_audit_category_cache (category_id, category, instance_id)
                    SELECT 
                        "ID"::BIGINT,
                        "CATEGORY",
                        instance_id
                    FROM load_raw.AO_C77861_AUDIT_CATEGORY_CACHE
                    WHERE "ID" IS NOT NULL
                """
            }

            for table in fact_tables:
                if table in fact_ddls:
                    try:
                        cur.execute(f"DROP TABLE IF EXISTS load.fact_{table} CASCADE")
                        cur.execute(fact_ddls[table])
                        logger.info(f"Created load.fact_{table}")
                    except Exception as e:
                        logger.warning(f"Failed to create fact_{table}: {str(e)}")
                        self.stats['errors'].append(f"Fact {table}: {str(e)}")
                        self.conn.rollback()
                        continue

            for table in dim_tables:
                if table in dim_ddls:
                    try:
                        dim_name = table.lower().replace('ao_', 'ao_').replace('_clean', '')
                        cur.execute(f"DROP TABLE IF EXISTS load.dim_{dim_name} CASCADE")
                        cur.execute(dim_ddls[table])
                        logger.info(f"Created load.dim_{dim_name}")
                    except Exception as e:
                        logger.warning(f"Failed to create dim_{dim_name}: {str(e)}")
                        self.stats['errors'].append(f"Dim {table}: {str(e)}")
                        self.conn.rollback()
                        continue
                else:
                    try:
                        cur.execute(f"""
                            DROP TABLE IF EXISTS load.dim_{table} CASCADE;
                            CREATE TABLE load.dim_{table} AS
                            SELECT * FROM load_raw.{table};
                            ALTER TABLE load.dim_{table} ADD COLUMN loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
                        """)
                        logger.info(f"Created load.dim_{table}")
                    except Exception as e:
                        logger.warning(f"Failed to create dim_{table}: {str(e)}")
                        self.stats['errors'].append(f"Dim {table}: {str(e)}")
                        self.conn.rollback()
                        continue

            self.conn.commit()

    def layer_4_analyst_transformation(self):
        """Layer 4: Apply business logic (e.g., story points, user mappings)"""
        # Story points for fact_jiraissue
        try:
            df = pd.read_sql("""
                SELECT fi.issue_id, cf.number_value AS story_points
                FROM load.fact_jiraissue fi
                LEFT JOIN load.fact_customfieldvalue cf ON fi.issue_id = cf.issue_id
                LEFT JOIN load_raw.customfield ON cf.customfield_id = customfield.id
                WHERE customfield.cfname ILIKE '%Story Points%' AND cf.instance_id = 1
            """, self.engine)
            df['story_points'] = df['story_points'].fillna(0).astype(float)
            with self.engine.connect() as conn:
                for idx, row in df.iterrows():
                    conn.execute("""
                        UPDATE load.fact_jiraissue
                        SET story_points = %s
                        WHERE issue_id = %s
                    """, (row['story_points'], row['issue_id']))
                conn.commit()
            logger.info("Updated story points in fact_jiraissue")
        except Exception as e:
            logger.warning(f"Story points update failed: {str(e)}")
            self.stats['errors'].append(f"Story points: {str(e)}")

        # User mappings for fact_jiraissue
        for col in ['reporter_id', 'assignee_id']:
            try:
                df_users = pd.read_sql(f"""
                    SELECT fi.issue_id, fi.{col}, u.user_id
                    FROM load.fact_jiraissue fi
                    LEFT JOIN load.dim_cwd_user u ON fi.{col} = u.user_id
                """, self.engine)
                with self.engine.connect() as conn:
                    for idx, row in df_users.iterrows():
                        if row['user_id'] is not None:
                            conn.execute(f"""
                                UPDATE load.fact_jiraissue
                                SET {col} = %s
                                WHERE issue_id = %s
                            """, (row['user_id'], row['issue_id']))
                    conn.commit()
                logger.info(f"Updated {col} in fact_jiraissue")
            except Exception as e:
                logger.warning(f"User mapping for {col} failed: {str(e)}")
                self.stats['errors'].append(f"User {col}: {str(e)}")

    def layer_5_semantic(self):
        """Layer 5: Create analyst-friendly views"""
        with self.conn.cursor() as cur:
            try:
                cur.execute("""
                    CREATE SCHEMA IF NOT EXISTS analytics
                """)
                cur.execute("""
                    CREATE OR REPLACE VIEW analytics.issues_with_details AS
                    SELECT 
                        fi.issue_id,
                        p.project_name,
                        COALESCE(fi.story_points, 0) AS story_points,
                        u.display_name AS assignee_name,
                        s.status_name AS status
                    FROM load.fact_jiraissue fi
                    JOIN load.dim_project p ON fi.project_id = p.project_id
                    LEFT JOIN load.dim_cwd_user u ON fi.assignee_id = u.user_id
                    LEFT JOIN load.dim_issuestatus s ON fi.status_id = s.id
                    WHERE fi.instance_id = 1
                """)
                logger.info("Created analytics.issues_with_details view")
                self.conn.commit()
            except Exception as e:
                logger.warning(f"View creation failed: {str(e)}")
                self.stats['errors'].append(f"View creation: {str(e)}")

    def verify_load(self):
        """Verify load schema"""
        with self.conn.cursor() as cur:
            cur.execute("""
                SELECT table_name, 
                       (SELECT COUNT(*) FROM load."%s") as record_count
                FROM information_schema.tables
                WHERE table_schema = 'load'
            """)
            results = cur.fetchall()
            for table, count in results:
                logger.info(f"load.{table}: {count:,} records")
                if count == 0 and table in ['fact_jiraissue', 'fact_worklog', 'fact_customfieldvalue']:
                    logger.warning(f"Empty table: {table}")
                    self.stats['errors'].append(f"Empty {table}")

    def run(self):
        """Execute all layers"""
        logger.info("Starting Universal Loader for 33 tables")
        try:
            logger.info("Layer 1: Raw Mirror")
            self.layer_1_raw_mirror()
            logger.info("Layer 2: Safe Load")
            self.layer_2_safe_load()
            logger.info("Layer 3: Schema Surgery")
            self.layer_3_schema_surgery()
            logger.info("Layer 4: Analyst Transformation")
            self.layer_4_analyst_transformation()
            logger.info("Layer 5: Semantic Layer")
            self.layer_5_semantic()
            logger.info("Verifying load")
            self.verify_load()
            duration = (datetime.now() - self.stats['start_time']).total_seconds()
            logger.info(f"\nLOADING COMPLETED!")
            logger.info(f"Duration: {duration:.2f} seconds")
            logger.info(f"Tables processed: {self.stats['tables_processed']}")
            logger.info(f"Records loaded: {self.stats['total_records']:,}")
            logger.info(f"Errors: {len(self.stats['errors'])}")
            if self.stats['errors']:
                logger.warning("Errors encountered:")
                for error in self.stats['errors'][:5]:
                    logger.warning(f"  - {error}")
        except Exception as e:
            logger.error(f"Fatal error: {e}")
            self.conn.rollback()
            raise
        finally:
            self.conn.close()
            self.engine.dispose()

if __name__ == "__main__":
    loader = UniversalLoader()
    loader.run()