#!/usr/bin/env python3
"""
 TRANSFORM INSERT - DATA TRANSFORMATION
Transforms data from staging to transform with approved transformations:
- Copy data excluding NULL/useless columns
- Extract numeric values from user columns (JIRAUSER123 → 123)
- Ensure data type consistency
- Add ETL tracking

TRANSFORMATIONS:
1.  Data copying (staging → transform, excluding dropped columns)
2.  User column numeric extraction (JIRAUSER123 → 123 for reporter/assignee/creator/lead/author)
3.  Data validation (ensure consistency during transformation)
4.  ETL tracking (instance_id, transformed_at)
"""

import psycopg2
import logging
from datetime import datetime
import sys
import os

# Add utilities directory to path for imports
utilities_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '05_UTILITIES')
sys.path.append(utilities_path)
from config_manager import ETLConfigManager

# Configuration du logging (fix Unicode issues)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('transform_insert.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class TransformDataProcessor:
    def __init__(self):
        """Initialiser le processeur de transformation"""
        self.stats = {
            'start_time': datetime.now(),
            'tables_processed': 0,
            'total_records': 0,
            'errors': []
        }
        
        # 33 tables fonctionnelles avec données (user specified list)
        self.all_critical_tables = [
            #  CORE_BUSINESS (9 tables)
            'jiraissue', 'project', 'component',
            'customfield', 'customfieldvalue', 'worklog', 'fileattachment',
            'issuelinktype', 'label',

            #  USERS_GROUPS (4 tables)
            'cwd_group', 'cwd_user', 'cwd_membership', 'projectroleactor',

            #  WORKFLOWS (3 tables)
            'jiraworkflows', 'workflowscheme', 'workflowschemeentity',

            #  CONFIGURATION (6 tables)
            'fieldconfigscheme', 'fieldconfiguration', 'fieldscreen', 'fieldscreentab',
            'permissionscheme', 'schemepermissions',

            #  LOOKUP_TABLES (6 tables)
            'priority', 'issuestatus', 'resolution', 'issuetype', 'projectrole',
            'pluginversion', 'managedconfigurationitem',

            #  AGILE_BOARDS (1 table)
            'AO_60DB71_RAPIDVIEW',

            #  JSM_AUDIT (3 tables)
            'AO_C77861_AUDIT_ENTITY', 'AO_C77861_AUDIT_ACTION_CACHE',
            'AO_C77861_AUDIT_CATEGORY_CACHE'
        ]
        
        # Colonnes utilisateur nécessitant extraction numérique
        self.user_columns = ['reporter', 'assignee', 'creator', 'lead', 'author']

    def connect_dw(self):
        """Connexion à la base de données DW"""
        try:
            # Use same config as DDL script
            parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            config_file = os.path.join(parent_dir, 'etl_config.json')
            config_manager = ETLConfigManager(config_file)
            db_config = config_manager.config["database"]
            dw_config = {
                'host': db_config['host'],
                'port': db_config['port'],
                'database': db_config['target_db'],
                'user': db_config['user'],
                'password': db_config['password']
            }
            return psycopg2.connect(**dw_config)
        except Exception as e:
            logger.error(f"Erreur connexion DW: {e}")
            raise

    def get_common_columns(self, table_name):
        """Récupérer les colonnes communes entre staging et transform"""
        conn = self.connect_dw()
        cursor = conn.cursor()

        try:
            transform_table_name = f"{table_name}_clean"

            # Colonnes staging (exclure ETL)
            cursor.execute("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_schema = 'staging' AND table_name = %s
                AND column_name NOT IN ('instance_id', 'extracted_at')
                ORDER BY ordinal_position
            """, (table_name,))
            staging_columns = [row[0] for row in cursor.fetchall()]

            # Colonnes transform (exclure ETL et user denorm)
            cursor.execute("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_schema = 'transform' AND table_name = %s
                AND column_name NOT IN ('instance_id', 'transformed_at')
                AND column_name NOT LIKE '%%_username'
                AND column_name NOT LIKE '%%_email'
                AND column_name NOT LIKE '%%_display_name'
                ORDER BY ordinal_position
            """, (transform_table_name,))
            transform_columns = [row[0] for row in cursor.fetchall()]

            # Colonnes communes avec gestion spéciale ID/id pour tables AO_
            common_columns = []
            for col in staging_columns:
                if col in transform_columns:
                    common_columns.append(col)
                elif col == 'ID' and 'id' in transform_columns and table_name.startswith('AO_'):
                    # Pour les tables AO_, mapper ID (staging) vers id (transform)
                    common_columns.append(col)  # Garder le nom staging pour la requête
                elif col == 'id' and 'ID' in transform_columns and table_name.startswith('AO_'):
                    # Cas inverse
                    common_columns.append(col)

            common_columns.sort()

            logger.info(f"   Staging: {len(staging_columns)} cols, Transform: {len(transform_columns)} cols, Common: {len(common_columns)} cols")
            return common_columns

        except Exception as e:
            logger.error(f"Erreur recuperation colonnes {table_name}: {e}")
            return []
        finally:
            conn.close()

    def get_user_columns_for_table(self, table_name):
        """Récupérer les colonnes utilisateur d'une table"""
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            cursor.execute("""
                SELECT column_name 
                FROM information_schema.columns
                WHERE table_schema = 'staging' AND table_name = %s
                AND column_name NOT IN ('instance_id', 'extracted_at')
                ORDER BY ordinal_position
            """, (table_name,))
            all_columns = [row[0] for row in cursor.fetchall()]
            
            # Filtrer les colonnes utilisateur
            user_cols = []
            for col in all_columns:
                if any(user_col.lower() in col.lower() for user_col in self.user_columns):
                    user_cols.append(col)
            
            return user_cols
            
        except Exception as e:
            logger.error(f" Erreur récupération colonnes utilisateur {table_name}: {e}")
            return []
        finally:
            conn.close()

    def get_user_info(self, user_key):
        """Récupérer les informations utilisateur depuis cwd_user (columns dropped - method disabled)"""
        # Note: This method is disabled because the referenced columns were dropped:
        # - lower_user_name (dropped from cwd_user)
        # - email_address (available as email_address)
        # - display_name (available as display_name)
        # User denormalization was removed from ETL pipeline
        return None, None, None

    def transform_table_data(self, table_name):
        """Transformer les données d'une table avec extraction numérique des colonnes utilisateur"""
        conn = self.connect_dw()
        cursor = conn.cursor()

        try:
            transform_table_name = f"{table_name}_clean"

            # Récupérer les colonnes communes
            common_columns = self.get_common_columns(table_name)

            if not common_columns:
                logger.warning(f"   {table_name}: Aucune colonne commune trouvee")
                self.stats['errors'].append(f"{table_name}: Aucune colonne commune")
                return 0

            # Identifier les colonnes utilisateur dans cette table
            user_columns_in_table = [col for col in common_columns if self.is_user_column(col)]

            if user_columns_in_table:
                logger.info(f"   {table_name}: Extraction numérique pour {len(user_columns_in_table)} colonnes utilisateur: {user_columns_in_table}")

            # Vider la table transform avant insertion
            cursor.execute(f'DELETE FROM transform."{transform_table_name}"')

            # Récupérer les données staging avec casting amélioré pour tables problématiques
            if table_name in ['AO_C77861_AUDIT_ENTITY', 'fieldconfigscheme', 'fieldconfiguration', 'managedconfigurationitem']:
                # Utiliser casting spécialisé pour tables avec problèmes de types
                select_parts = []
                for col in common_columns:
                    if table_name == 'AO_C77861_AUDIT_ENTITY' and col in ['PRIMARY_RESOURCE_ID', 'SECONDARY_RESOURCE_ID', 'USER_ID']:
                        # Casting conditionnel pour colonnes mixtes
                        select_parts.append(f'''
                            CASE
                                WHEN "{col}"::TEXT ~ '^[0-9]+$' THEN CAST("{col}" AS NUMERIC)
                                ELSE NULL
                            END as "{col}"
                        ''')
                    elif table_name in ['fieldconfigscheme', 'fieldconfiguration'] and col in ['fieldid', 'customfield']:
                        # Casting conditionnel pour fieldid/customfield
                        select_parts.append(f'''
                            CASE
                                WHEN "{col}"::TEXT ~ '^[0-9]+$' THEN CAST("{col}" AS NUMERIC)
                                ELSE NULL
                            END as "{col}"
                        ''')
                    elif table_name == 'managedconfigurationitem' and col == 'item_id':
                        # Casting conditionnel pour item_id
                        select_parts.append(f'''
                            CASE
                                WHEN "{col}"::TEXT ~ '^[0-9]+$' THEN CAST("{col}" AS NUMERIC)
                                ELSE NULL
                            END as "{col}"
                        ''')
                    else:
                        select_parts.append(f'"{col}"')

                columns_list = ', '.join(select_parts)
            else:
                # Casting standard pour autres tables
                columns_list = ', '.join([f'"{col}"' for col in common_columns])

            select_sql = f'''
                SELECT {columns_list}, instance_id
                FROM staging."{table_name}"
                WHERE instance_id = 1
            '''

            cursor.execute(select_sql)
            staging_data = cursor.fetchall()

            if not staging_data:
                logger.warning(f"   {table_name}: Aucune donnee staging")
                return 0

            # Transformer les données avec extraction numérique
            transformed_data = []
            extraction_stats = {col: {'extracted': 0, 'failed': 0} for col in user_columns_in_table}

            for row in staging_data:
                transformed_row = list(row)  # Copy the row

                # Process user columns for numeric extraction
                for i, col_name in enumerate(common_columns):
                    if col_name in user_columns_in_table:
                        original_value = transformed_row[i]
                        numeric_value = self.extract_numeric_from_user_value(original_value)

                        if numeric_value is not None:
                            transformed_row[i] = numeric_value
                            extraction_stats[col_name]['extracted'] += 1
                        else:
                            transformed_row[i] = None  # Set to NULL if extraction fails
                            extraction_stats[col_name]['failed'] += 1

                transformed_data.append(transformed_row)

            # Log extraction statistics
            for col_name, stats in extraction_stats.items():
                if stats['extracted'] > 0 or stats['failed'] > 0:
                    logger.info(f"     {col_name}: {stats['extracted']} extracted, {stats['failed']} failed")

            # Préparer l'insertion
            insert_columns = common_columns + ['instance_id']
            columns_insert = ', '.join([f'"{col}"' for col in insert_columns])
            placeholders = ', '.join(['%s'] * len(insert_columns))

            insert_sql = f'''
                INSERT INTO transform."{transform_table_name}"
                ({columns_insert}, transformed_at)
                VALUES ({placeholders}, CURRENT_TIMESTAMP)
            '''

            # Insertion par batch
            cursor.executemany(insert_sql, transformed_data)
            total_inserted = len(transformed_data)

            conn.commit()

            self.stats['tables_processed'] += 1
            self.stats['total_records'] += total_inserted

            logger.info(f"   SUCCES {table_name}: {total_inserted:,} records transformes")
            return total_inserted

        except Exception as e:
            logger.error(f"Erreur transformation {table_name}: {e}")
            self.stats['errors'].append(f"{table_name}: {e}")
            conn.rollback()
            return 0
        finally:
            conn.close()

    def is_user_column(self, column_name):
        """Check if column contains user references that need numeric extraction"""
        return any(user_col.lower() in column_name.lower() for user_col in self.user_columns)

    def extract_numeric_from_user_value(self, value):
        """Extract numeric portion from user values like JIRAUSER123 -> 123"""
        if not value:
            return None

        import re
        # Extract numeric portion from strings like JIRAUSER123, user123, etc.
        match = re.search(r'(\d+)', str(value))
        if match:
            try:
                return int(match.group(1))
            except ValueError:
                return None
        return None

    def denormalize_user_columns(self, table_name, user_columns):
        """Extract numeric portions from user columns"""
        logger.debug(f"    User numeric extraction for {table_name}")
        return

    def process_all_tables(self):
        """Traiter toutes les tables"""
        logger.info(f"\n TRANSFORMATION DE 33 TABLES")

        for i, table_name in enumerate(self.all_critical_tables, 1):
            logger.info(f" [{i:2d}/33] Transformation {table_name}")

            # Transformer les données
            records_count = self.transform_table_data(table_name)

            if records_count > 0:
                logger.info(f"    {records_count:,} records transformés")
            else:
                logger.warning(f"    Aucun record transformé")

    def verify_transformation(self):
        """Vérifier la transformation"""
        logger.info(f"\n VÉRIFICATION TRANSFORMATION")

        conn = self.connect_dw()
        cursor = conn.cursor()

        try:
            # Compter les tables avec données
            cursor.execute("""
                SELECT
                    table_name,
                    (SELECT COUNT(*) FROM transform."" || table_name || "") as record_count
                FROM information_schema.tables
                WHERE table_schema = 'transform'
                ORDER BY table_name
            """)

            # Alternative: compter manuellement
            tables_with_data = 0
            total_records = 0

            for table_name in self.all_critical_tables:
                transform_table_name = f"{table_name}_clean"
                try:
                    cursor.execute(f'SELECT COUNT(*) FROM transform."{transform_table_name}"')
                    count = cursor.fetchone()[0]
                    if count > 0:
                        tables_with_data += 1
                        total_records += count
                except:
                    pass

            logger.info(f"    Tables avec données: {tables_with_data}/33")
            logger.info(f"    Total records: {total_records:,}")

            return tables_with_data, total_records

        except Exception as e:
            logger.error(f" Erreur vérification: {e}")
            return 0, 0
        finally:
            conn.close()

if __name__ == "__main__":
    print(" TRANSFORM INSERT - DATA TRANSFORMATION")
    print("=" * 70)
    print(" Objectif: Transformer données staging → transform")
    print("=" * 70)

    try:
        # Créer le processeur
        processor = TransformDataProcessor()

        # Traiter toutes les tables
        processor.process_all_tables()

        # Vérifier la transformation
        tables_with_data, total_records = processor.verify_transformation()

        # Statistiques finales
        duration = (datetime.now() - processor.stats['start_time']).total_seconds()

        logger.info(f"\n TRANSFORMATION TERMINÉE!")
        logger.info(f" Durée: {duration:.2f} secondes")
        logger.info(f" Tables traitées: {processor.stats['tables_processed']}")
        logger.info(f" Records transformés: {processor.stats['total_records']:,}")

        logger.info(f" Erreurs: {len(processor.stats['errors'])}")

        if processor.stats['errors']:
            logger.warning(f" Erreurs rencontrées:")
            for error in processor.stats['errors'][:5]:
                logger.warning(f"   - {error}")

        if tables_with_data == 33:
            logger.info(" SUCCÈS: Toutes les 33 tables transformées!")
        else:
            logger.warning(f" ATTENTION: {tables_with_data}/33 tables avec données")

    except Exception as e:
        logger.error(f" Erreur fatale: {e}")
        sys.exit(1)